# Summary

* [Overview](README.md)

### Devtron

* [Setup](setup/install/setup.md)
  * [Installing Devtron](setup/install/README.md)
    * [Install Devtron with CI/CD integration](setup/install/install-devtron-with-cicd.md)
    * [Install Devtron](setup/install/install-devtron.md)
    * [Installation Configurations](setup/install/installation-configuration.md)
    * [Override Configurations](setup/install/override-default-devtron-installation-configs.md)
    * [Ingress setup for devtron installation](setup/install/ingress-setup.md)
  * [Devtron Integrations](user-guide/stack-manager.md)
  * [Global Configurations](user-guide/global-configurations/README.md)
    * [GitOps](user-guide/global-configurations/gitops.md)
    * [Git Accounts](user-guide/global-configurations/git-accounts.md)
    * [Container Registries](user-guide/global-configurations/docker-registries.md)
    * [Chart Repositories](user-guide/global-configurations/chart-repo.md)
    * [Custom Charts](user-guide/global-configurations/custom-charts.md)
    * [Cluster And Environments](user-guide/global-configurations/cluster-and-environments.md)
    * [Projects](user-guide/global-configurations/projects.md)
    * [SSO Login Service](user-guide/global-configurations/sso-login.md)
    * [Authorization](user-guide/global-configurations/authorization/README.md)
      * [User Permissions](user-guide/global-configurations/authorization/user-access.md)
      * [Permission Groups](user-guide/global-configurations/authorization/permission-groups.md)
      * [API Tokens](user-guide/global-configurations/authorization/api-tokens.md)
    * [Manage Notification](user-guide/global-configurations/manage-notification.md)
    * [Deploy a sample App](user-guide/Deploy-sample-app/nodejs_app.md)
    * [External links](user-guide/global-configurations/external-links.md)
  * [Devtron Upgrade](setup/upgrade/README.md)
    * [Update Devtron from Devtron UI](setup/upgrade/upgrade-devtron-ui.md)
    * [0.4.x-0.4.x](setup/upgrade/devtron-upgrade-0.4.x-0.4.x.md)
    * [0.3.x-0.4.x](setup/upgrade/devtron-upgrade-0.3.x-0.4.x.md)
    * [0.3.x-0.3.x](setup/upgrade/devtron-upgrade-0.3.x-0.3.x.md)
    * [0.2.x-0.3.x](setup/upgrade/devtron-upgrade-0.2.x-0.3.x.md)
* [User Guide](user-guide/creating-application/userguide.md)
  * [Creating Application](user-guide/creating-application/README.md)
    * [Git Repository](user-guide/creating-application/git-material.md)
    * [Docker Build Configuration](user-guide/creating-application/docker-build-configuration.md)
    * [Deployment Template](user-guide/creating-application/deployment-template.md)
      * [Rollout Deployment](user-guide/creating-application/deployment-template/rollout-deployment.md)
      * [Job and Cronjob](user-guide/creating-application/deployment-template/job-and-cronjob.md)
    * [Workflow](user-guide/creating-application/workflow/README.md)
      * [CI Pipeline](user-guide/creating-application/workflow/ci-pipeline.md)
        * [Pre-Build/Post-Build tasks](user-guide/creating-application/workflow/ci-build-pre-post-plugins.md)
      * [CI Pipeline (Legacy)](user-guide/creating-application/workflow/ci-pipeline-legacy.md)
      * [Automated test suite integration in CI](user-guide/creating-application/workflow/automated-test.md)
      * [CD Pipeline](user-guide/creating-application/workflow/cd-pipeline.md)
    * [Config Maps](user-guide/creating-application/config-maps.md)
    * [Secrets](user-guide/creating-application/secrets.md)
    * [Environment Overrides](user-guide/creating-application/environment-overrides.md)
    * [Application Metrics](user-guide/creating-application/app-metrics.md)
  * [Application Details](user-guide/creating-application/app-details.md)
  * [Cloning Application](user-guide/cloning-application.md)
  * [Deploying Application](user-guide/deploying-application/README.md)
    * [Triggering CI](user-guide/deploying-application/triggering-ci.md)
    * [Triggering CD](user-guide/deploying-application/triggering-cd.md)
  * [Deploy Chart](user-guide/deploy-chart/README.md)
    * [Overview Of Charts](user-guide/deploy-chart/overview-of-charts.md)
    * [Charts Create Update Upgrade Deploy Delete](user-guide/deploy-chart/deployment-of-charts.md)
    * [Chart Group Create Edit And Deploy](user-guide/deploy-chart/chart-group.md)
    * [Examples](user-guide/deploy-chart/examples/README.md)
      * [Deploying Mysql Helm Chart](user-guide/deploy-chart/examples/deploying-mysql-helm-chart.md)
      * [Deploying MongoDB Helm Chart](user-guide/deploy-chart/examples/deploying-mongodb-helm-chart.md)
  * [Debugging Deployment And Monitoring](user-guide/debugging-deployment-and-monitoring.md)
  * [Namespaces And Environments](user-guide/namespaces-and-environments.md)
  * [Security Features](user-guide/security-features.md)
  * [Deleting Application](user-guide/deleting-application.md)
  * [Bulk Update](user-guide/bulk-update.md)
  * [Command Bar](user-guide/command-bar.md)
  * [Use Cases](user-guide/use-cases/README.md)
    * [Devtron Generic Helm Chart To Run Cron Job Or One Time Job](user-guide/use-cases/devtron-generic-helm-chart-to-run-cron-job-or-one-time-job.md)
    * [Connect SpringBoot with Mysql Database](user-guide/use-cases/connect-springboot-with-mysql-database.md)
    * [Connect Expressjs With Mongodb Database](user-guide/use-cases/connect-expressjs-with-mongodb-database.md)
    * [Connect Django With Mysql Database](user-guide/use-cases/connect-django-with-mysql-database.md)
  * [Telemetry Overview](user-guide/telemetry.md)
* [FAQs & Troubleshooting](FAQs/faqs.md)
  * [Devtron Troubleshooting](FAQs/devtron-troubleshoot.md)
