openapi: "3.0.0"
info:
  version: 1.0.0
  title: Devtron Labs
  description: Devtron API for deployment template management
  termsOfService: https://devtron.ai/terms/
  contact:
    name: Devtron Labs
    email: <EMAIL>
    url: https://devtron.ai
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html
servers:
  - url: http://localhost:8080/orchestrator
paths:
  /deployment/template/validate:
    post:
      description: upload template file from this api to validate.
      requestBody:
        description: form-data as request body
        required: true
        content:
          multipart/form-data:
            schema:
              properties:
                binaryFile:
                  type: string
                  format: binary
                  description: zipped chart template file
      responses:
        '200':
          description: template file upload response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    $ref: '#/components/schemas/UploadTemplateResponse'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /deployment/template/upload:
    put:
      description: upload template file from this api.
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UploadTemplateRequest'
      responses:
        '200':
          description: template file upload response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: string
                    description: result
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /deployment/template/fetch:
    get:
      summary: Returns all charts
      description: all the chart template uploaded by user
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: array
                    description: list of charts
                    items:
                      $ref: '#/components/schemas/Chart'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    UploadTemplateRequest:
      type: object
      properties:
        fileId:
          type: string
        action:
          type: string
    UploadTemplateResponse:
      type: object
      properties:
        chartName:
          type: string
        description:
          type: string
        fileId:
          type: string
        action:
          type: string
        message:
          type: string
    Chart:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        count:
          type: integer
    ErrorResponse:
      required:
        - code
        - message
      properties:
        code:
          type: integer
          description: Error code
        message:
          type: string
          description: Error message