openapi: "3.0.0"
info:
  version: 1.0.0
  title: Devtron Labs
  description: Devtron API for app store management
  termsOfService: https://devtron.ai/terms/
  contact:
    name: Devtron Labs
    email: <EMAIL>
    url: https://devtron.ai
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html
servers:
  - url: http://localhost:8080/orchestrator
paths:
  /app-store/discover:
    get:
      description: this api will return all the charts from charts repos.
      parameters:
        - name: includeDeprecated
          in: query
          description: include deprecated charts
          required: false
          schema:
            type: boolean
        - name: chartRepoId
          in: query
          description: chart repository ids (comma separated)
          required: false
          schema:
            type: string
        - name: registryId
          in: query
          description: registry ids (comma separated)
          required: false
          schema:
            type: string
        - name: appStoreName
          in: query
          description: app store name filter
          required: false
          schema:
            type: string
        - name: offset
          in: query
          description: offset for pagination
          required: false
          schema:
            type: integer
        - name: size
          in: query
          description: size for pagination
          required: false
          schema:
            type: integer
        - name: chartCategoryId
          in: query
          description: chart category ids (comma separated)
          required: false
          schema:
            type: string
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: array
                    description: app store applications with versions
                    items:
                      $ref: '#/components/schemas/AppStoreWithVersion'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'






# components mentioned below
components:
  schemas:
    AppStoreWithVersion:
      type: object
      properties:
        id:
          type: integer
          description: app store id
        name:
          type: string
          description: app store name
        appStoreApplicationVersionId:
          type: integer
          description: app store version id
        chartGitLocation:
          type: string
          description: chart git repo location
        chartName:
          type: string
          description: chart name
        chartRepoId:
          type: integer
          description: app store and chart repo link id
        deprecated:
          type: boolean
          description: deprecated app store flag
        description:
          type: string
          description: app store description, short summary
        icon:
          type: string
          description: app store icon link
        createdOn:
          type: string
          format: date-time
          description: created on
        updatedOn:
          type: string
          format: date-time
          description: modification date
        version:
          type: string
          description: app store version
        active:
          type: boolean
          description: active app store
        chartRepoName:
          type: string
          description: chart repository name
        isChartRepoActive:
          type: boolean
          description: whether chart repository is active

    ErrorResponse:
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error code
        status:
          type: string
          description: Error message
        errors:
          type: array
          description: errors
          items:
            $ref: '#/components/schemas/Error'

    Error:
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error internal code
        internalMessage:
          type: string
          description: Error internal message
        userMessage:
          type: string
          description: Error user message