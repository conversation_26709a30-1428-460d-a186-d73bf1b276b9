openapi: "3.0.0"
info:
  version: 1.0.0
  title: Devtron Labs
  description: Devtron API for application management
  termsOfService: https://devtron.ai/terms/
  contact:
    name: Devtron Labs
    email: <EMAIL>
    url: https://devtron.ai
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html
servers:
  - url: http://localhost:8080/orchestrator
paths:
  /core/v1beta1/application:
    post:
      description: create new application
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAppRequest'
      responses:
        '200':
          description: App create response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: string
                    description: success message
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /app/edit:
    post:
      description: update application projects and labels
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/App'
      responses:
        '200':
          description: App update response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: object
                    description: string
                    allOf:
                      - type: object
                        properties:
                          id:
                            type: integer
                            description: unique application id
                        required:
                          - id
                      - $ref: '#/components/schemas/App'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /app/list:
    post:
      description: app listing, collection of deployed applications or undeployed or incomplete configured apps.
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AppListingRequest'
      responses:
        '200':
          description: App create response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: object
                    description: string
                    required:
                      -  appCount
                      -  appContainers
                      -  deploymentGroup
                    properties:
                      appCount:
                        type: integer
                        description: app count, total number of apps available based on filter provided in request.
                      appContainers:
                        type: array
                        description: app containers
                        items:
                          $ref: '#/components/schemas/AppContainer'
                      deploymentGroup:
                        type: object
                        description: deployment group
                        $ref: '#/components/schemas/DeploymentGroup'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /app/edit/projects:
    post:
      description: update project for app
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AppProjectUpdateRequest'
      responses:
        '200':
          description: App update response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: object
                    description: string
                    $ref: '#/components/schemas/AppProjectUpdateRequest'

        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
# components mentioned below
components:
  schemas:
    AppLabel:
      type: object
      required:
        - key
        - value
      properties:
        key:
          type: string
          description: label key
        value:
          type: string
          description: label value
        propagate:
          type: boolean
          description: Whether to propagate to kubernetes resources

    CreateAppRequest:
      type: object
      required:
        - metadata
      properties:
        metadata:
          $ref: '#/components/schemas/App'
        appWorkflows:
          type: array
          description: app workflows
          items:
            type: object
        environmentOverrides:
          type: array
          description: environment overrides
          items:
            type: object

    App:
      type: object
      required:
        - appName
        - teamId
        - templateId
      properties:
        appName:
          type: string
          description: app name
        teamId:
          type: integer
          description: project id
        templateId:
          type: integer
          description: reference app id, used for clone, set default value 0 for blank app.
        labels:
          type: array
          description: each apps may have multiple labels. these are optional.
          items:
            $ref: '#/components/schemas/AppLabel'
    AppProjectUpdateRequest:
      type: object
      required:
        - teamId
        - appId
      properties:
        teamId:
          type: integer
          description: project id
        appId:
          type: array
          description: team id, teams ids are projects ids
          items:
            type: integer

    AppListingRequest:
      type: object
      required:
        - offset
        - size
      properties:
        appNameSearch:
          type: string
          description: app name search, wildcard match
        offset:
          type: integer
          description: offset
        size:
          type: integer
          description: result size
        sortBy:
          type: string
          description: sort by
        sortOrder:
          type: string
          description: sort order
        environments:
          type: array
          description: environment id
          items:
            type: integer
        teams:
          type: array
          description: team id, teams ids are projects ids
          items:
            type: integer
        labels:
          type: array
          description: app labels
          items:
            type: string
        statuses:
          type: array
          description: status
          items:
            type: string

    AppContainer:
      type: object
      required:
        - appId
        - appName
        - environments
      properties:
        appId:
          type: integer
          description: app id
        appName:
          type: string
          description: app name
        environments:
          type: array
          items:
            $ref: '#/components/schemas/EnvContainer'

    EnvContainer:
      type: object
      required:
        - appId
        - appName
        - environmentId
        - environmentName
      properties:
        appId:
          type: integer
          description: app id
        appName:
          type: string
          description: app name
        cdStageStatus:
          type: string
          description: app name
        dataSource:
          type: string
          description: app name
        ciArtifactId:
          type: integer
          description: app name
        deleted:
          type: boolean
          description: app name
        environmentId:
          type: integer
          description: app name
        environmentName:
          type: string
          description: app name
        status:
          type: string
          description: app name
        appStatus:
          type: string
          description: app status for this environment
        postStageStatus:
          type: string
          description: app name
        preStageStatus:
          type: string
          description: app name
        lastDeployedTime:
          type: string
          description: deployed time
        materialInfo:
          type: array
          items:
            type: object

    DeploymentGroup:
      type: object
      required:
        - id
      properties:
        id:
          type: integer
          description: id
        ciPipelineId:
          type: integer
          description: ciPipelineId
        environmentId:
          type: integer
          description: environmentId
        appCount:
          type: integer
          description: appCount
        name:
          type: string
          description: name
        noOfApps:
          type: string
          description: noOfApps


    ErrorResponse:
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error code
        status:
          type: string
          description: Error message
        errors:
          type: array
          description: errors
          items:
            $ref: '#/components/schemas/Error'

    Error:
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error internal code
        internalMessage:
          type: string
          description: Error internal message
        userMessage:
          type: string
          description: Error user message