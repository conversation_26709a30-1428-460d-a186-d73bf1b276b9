openapi: "3.0.0"
info:
  title: Devtron Labs
  description: Devtron API for job management
  version: "1.0"
  termsOfService: https://devtron.ai/terms/
  contact:
    name: Devtron Labs
    email: <EMAIL>
    url: https://devtron.ai
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html
servers:
  - url: http://localhost:8080/orchestrator
paths:
  /job:
    post:
      description: Create and clones a job
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateJob"
      responses:
        '200':
          description: Used to give response once a job is created
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    $ref: "#/components/schemas/ActionResponse"
  /job/list:
    post:
      description: Get the list of all the jobs by applying filter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/JobListRequest"
      responses:
        '200':
          description: Used to give response of list of jobs
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    $ref: "#/components/schemas/JobListResponse"
  /job/ci-pipeline/list/{jobId}:
    get:
      description: fetch details of job ci-pipelines for the overview page
      parameters:
        - name: jobId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Job CI pipeline details
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: array
                    items:
                      $ref: "#/components/schemas/JobCiPipeline"
components:
  schemas:
    CreateJob:
      type: object
      required:
        - appName
        - teamId
        - isJob
      properties:
        appName:
          type: string
          description: Used to give the name of the job
          example: "my-job-1"
        isJob:
          type: boolean
          description: States whether its a job or an app
          example: true
        teamId:
          type: integer
          description: Used to give team id
          example: 1
        templateId:
          type: integer
          description: Used to give the id of the job it wants to clone
          example: 18
        labels:
          type: array
          description: Job labels
          items:
            $ref: "#/components/schemas/JobLabel"
        description:
          type: string
          description: Used to give the description of the job once it is made.
          example: "This is my first Job"

    JobLabel:
      type: object
      properties:
        key:
          type: string
          example: "hello"
        value:
          type: string
          example: "world"
        propagate:
          type: boolean
          example: false
    ActionResponse:
      type: object
      properties:
        id:
          type: integer
          description: Used to give the id of job once its created
          example: 25
        appName:
          type: string
          description: Used to give the name of job once its created
          example: "my-job-1"
        material:
          type: array
          description: Git materials
          items:
            $ref: "#/components/schemas/GitMaterial"
        teamId:
          type: integer
          description: Used to give the team id
          example: 1
        templateId:
          type: integer
          description: Used to give the templateId
          example: 0
        description:
          type: string
          description: Used to give the description of the job once it is made.
          example: "This is my first Job"
        isJob:
          type: boolean
          description: used to tell whether it is a job or an app
          example: true

    GitMaterial:
      type: object
      properties:
        name:
          type: string
        url:
          type: string
        id:
          type: integer
        gitProviderId:
          type: integer
        checkoutPath:
          type: string
        fetchSubmodules:
          type: boolean
        isUsedInCiConfig:
          type: boolean
    JobListRequest:
      type: object
      properties:
        teams:
          type: array
          description: used to give the project id
          items:
            type: integer
          example: [1,2]
        appStatuses:
          type: array
          description: used to give the filter of app ci-build status
          items:
            type: string
          example: ["Succeeded", "Starting"]
        sortBy:
          type: string
          description: used to give the sort by constraint
          example: "appNameSort"
        sortOrder:
          type: string
          description: used to give the sort order
          example: "ASC"
        offset:
          type: integer
          description: used to give the number from which we want our job (if the offset is 20 means we want list of jobs from 20)
          example: 0
        size:
          type: integer
          description: used to give the number of jobs we want
          example: 20

    JobListResponse:
      type: object
      properties:
        jobContainers:
          type: array
          description: List of job containers
          items:
            $ref: "#/components/schemas/JobContainer"
        jobCount:
          type: integer
          description: Total count of jobs

    JobContainer:
      type: object
      properties:
        jobId:
          type: integer
        jobName:
          type: string
        description:
          type: string
        ciPipelines:
          type: array
          items:
            $ref: "#/components/schemas/JobCiPipeline"

    JobCiPipeline:
      type: object
      properties:
        ciPipelineId:
          type: integer
        status:
          type: string
        lastRunAt:
          type: string
          format: date-time
        lastSuccessAt:
          type: string
          format: date-time








