openapi: "3.0.0"
info:
  version: 1.0.0
  title: Devtron Labs
paths:
  /orchestrator/app-store/installed-app:
    get:
      description: deployed chart listing, with search filters
      parameters:
        - name: envIds
          in: query
          description: environment ids
          required: false
          schema:
            type: string
        - name: clusterIds
          in: query
          description: cluster ids
          required: false
          schema:
            type: string
        - name: chartRepoId
          in: query
          description: chart repo ids
          required: false
          schema:
            type: string
        - name: appStoreName
          in: query
          description: chart name
          required: false
          schema:
            type: string
        - name: appName
          in: query
          description: chart name as app name for devtron
          required: false
          schema:
            type: string
        - name: onlyDeprecated
          in: query
          description: show only deprecated or all
          required: false
          schema:
            type: boolean
        - name: offset
          in: query
          description: offset for result set
          required: false
          schema:
            type: integer
        - name: size
          in: query
          description: total request size.
          required: false
          schema:
            type: integer
        - name: appStatuses
          in: query
          description: app statuses filter
          required: false
          schema:
            type: string
      responses:
        '200':
          description: deployed chart listing, with search filters
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    $ref: '#/components/schemas/AppListDetail'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /orchestrator/app-store/installed-app/notes/{installed-app-id}/{env-id}:
    get:
      description: Used to fetch notes.txt for helm charts deployed via gitOps
      parameters:
        - name: env-id
          in: path
          description: it is an environment id of app
          required: true
          schema:
            type: integer
        - name: installed-app-id
          in: path
          description: it is a installed application id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: if it is able to fetch the notes.txt then status will be ok
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    $ref: '#/components/schemas/Notes'
        '500':
          description: error while fetching notes.txt



# components mentioned below
components:
  schemas:
    AppListDetail:
      type: object
      properties:
        clusterIds:
          type: array
          description: clusters to which result corresponds
          items:
            type: integer
        applicationType:
          type: string
          description: application type inside the array
          enum: [DEVTRON-CHART-STORE, DEVTRON-APP, HELM-APP]
        errored:
          type: boolean
          description: if data fetch for that cluster produced error
        errorMsg:
          type: string
          description: error msg if client failed to fetch
        helmApps:
          type: array
          description: all helm app list
          items:
            $ref: '#/components/schemas/HelmAppDetails'
        devtronApps:
          type: array
          description: all devtron app list
          items:
            type: object

    HelmAppDetails:
      type: object
      properties:
        lastDeployedAt:
          type: string
          format: date-time
          description: time when this application was last deployed/updated
        appName:
          type: string
          description: name of the helm application/helm release name
        appId:
          type: string
          description: unique identifier for app
        chartName:
          type: string
          description: name of the chart
        chartAvatar:
          type: string
          description: url/location of the chart icon
        projectId:
          type: integer
          description: unique identifier for the project, APP with no project will have id 0
        chartVersion:
          type: string
          description: chart version
        environmentDetail:
          $ref: '#/components/schemas/EnvironmentDetails'
        appStatus:
          type: string
          description: application status

    EnvironmentDetails:
      type: object
      properties:
        clusterName:
          type: string
          description: cluster corresponding to the environment where application is deployed
        clusterId:
          type: integer
          description: clusterId corresponding to the environment where application is deployed
        namespace:
          type: string
          description: namespace where application is deployed
        isVirtualEnvironment:
          type: boolean
          description: whether environment is virtual or not

    Notes:
      type: object
      properties:
        gitOpsNotes:
          type: string
          description: notes content

    ErrorResponse:
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error code
        status:
          type: string
          description: Error message
        errors:
          type: array
          description: errors
          items:
            $ref: '#/components/schemas/Error'

    Error:
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error internal code
        internalMessage:
          type: string
          description: Error internal message
        userMessage:
          type: string
          description: Error user message