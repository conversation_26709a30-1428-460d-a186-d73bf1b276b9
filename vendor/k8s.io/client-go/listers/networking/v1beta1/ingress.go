/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by lister-gen. DO NOT EDIT.

package v1beta1

import (
	networkingv1beta1 "k8s.io/api/networking/v1beta1"
	labels "k8s.io/apimachinery/pkg/labels"
	listers "k8s.io/client-go/listers"
	cache "k8s.io/client-go/tools/cache"
)

// IngressLister helps list Ingresses.
// All objects returned here must be treated as read-only.
type IngressLister interface {
	// List lists all Ingresses in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*networkingv1beta1.Ingress, err error)
	// Ingresses returns an object that can list and get Ingresses.
	Ingresses(namespace string) IngressNamespaceLister
	IngressListerExpansion
}

// ingressLister implements the IngressLister interface.
type ingressLister struct {
	listers.ResourceIndexer[*networkingv1beta1.Ingress]
}

// NewIngressLister returns a new IngressLister.
func NewIngressLister(indexer cache.Indexer) IngressLister {
	return &ingressLister{listers.New[*networkingv1beta1.Ingress](indexer, networkingv1beta1.Resource("ingress"))}
}

// Ingresses returns an object that can list and get Ingresses.
func (s *ingressLister) Ingresses(namespace string) IngressNamespaceLister {
	return ingressNamespaceLister{listers.NewNamespaced[*networkingv1beta1.Ingress](s.ResourceIndexer, namespace)}
}

// IngressNamespaceLister helps list and get Ingresses.
// All objects returned here must be treated as read-only.
type IngressNamespaceLister interface {
	// List lists all Ingresses in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*networkingv1beta1.Ingress, err error)
	// Get retrieves the Ingress from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*networkingv1beta1.Ingress, error)
	IngressNamespaceListerExpansion
}

// ingressNamespaceLister implements the IngressNamespaceLister
// interface.
type ingressNamespaceLister struct {
	listers.ResourceIndexer[*networkingv1beta1.Ingress]
}
