/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1beta1

// EndpointConditionsApplyConfiguration represents a declarative configuration of the EndpointConditions type for use
// with apply.
type EndpointConditionsApplyConfiguration struct {
	Ready       *bool `json:"ready,omitempty"`
	Serving     *bool `json:"serving,omitempty"`
	Terminating *bool `json:"terminating,omitempty"`
}

// EndpointConditionsApplyConfiguration constructs a declarative configuration of the EndpointConditions type for use with
// apply.
func EndpointConditions() *EndpointConditionsApplyConfiguration {
	return &EndpointConditionsApplyConfiguration{}
}

// WithReady sets the Ready field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Ready field is set to the value of the last call.
func (b *EndpointConditionsApplyConfiguration) WithReady(value bool) *EndpointConditionsApplyConfiguration {
	b.Ready = &value
	return b
}

// WithServing sets the Serving field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Serving field is set to the value of the last call.
func (b *EndpointConditionsApplyConfiguration) WithServing(value bool) *EndpointConditionsApplyConfiguration {
	b.Serving = &value
	return b
}

// WithTerminating sets the Terminating field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Terminating field is set to the value of the last call.
func (b *EndpointConditionsApplyConfiguration) WithTerminating(value bool) *EndpointConditionsApplyConfiguration {
	b.Terminating = &value
	return b
}
