//go:build !ignore_autogenerated
// +build !ignore_autogenerated

/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by conversion-gen. DO NOT EDIT.

package v1

import (
	unsafe "unsafe"

	authenticationv1 "k8s.io/api/authentication/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	conversion "k8s.io/apimachinery/pkg/conversion"
	runtime "k8s.io/apimachinery/pkg/runtime"
	types "k8s.io/apimachinery/pkg/types"
	authentication "k8s.io/kubernetes/pkg/apis/authentication"
)

func init() {
	localSchemeBuilder.Register(RegisterConversions)
}

// RegisterConversions adds conversion functions to the given scheme.
// Public to allow building arbitrary schemes.
func RegisterConversions(s *runtime.Scheme) error {
	if err := s.AddGeneratedConversionFunc((*authenticationv1.BoundObjectReference)(nil), (*authentication.BoundObjectReference)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_BoundObjectReference_To_authentication_BoundObjectReference(a.(*authenticationv1.BoundObjectReference), b.(*authentication.BoundObjectReference), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authentication.BoundObjectReference)(nil), (*authenticationv1.BoundObjectReference)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_authentication_BoundObjectReference_To_v1_BoundObjectReference(a.(*authentication.BoundObjectReference), b.(*authenticationv1.BoundObjectReference), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authenticationv1.SelfSubjectReview)(nil), (*authentication.SelfSubjectReview)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_SelfSubjectReview_To_authentication_SelfSubjectReview(a.(*authenticationv1.SelfSubjectReview), b.(*authentication.SelfSubjectReview), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authentication.SelfSubjectReview)(nil), (*authenticationv1.SelfSubjectReview)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_authentication_SelfSubjectReview_To_v1_SelfSubjectReview(a.(*authentication.SelfSubjectReview), b.(*authenticationv1.SelfSubjectReview), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authenticationv1.SelfSubjectReviewStatus)(nil), (*authentication.SelfSubjectReviewStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_SelfSubjectReviewStatus_To_authentication_SelfSubjectReviewStatus(a.(*authenticationv1.SelfSubjectReviewStatus), b.(*authentication.SelfSubjectReviewStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authentication.SelfSubjectReviewStatus)(nil), (*authenticationv1.SelfSubjectReviewStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_authentication_SelfSubjectReviewStatus_To_v1_SelfSubjectReviewStatus(a.(*authentication.SelfSubjectReviewStatus), b.(*authenticationv1.SelfSubjectReviewStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authenticationv1.TokenRequest)(nil), (*authentication.TokenRequest)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TokenRequest_To_authentication_TokenRequest(a.(*authenticationv1.TokenRequest), b.(*authentication.TokenRequest), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authentication.TokenRequest)(nil), (*authenticationv1.TokenRequest)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_authentication_TokenRequest_To_v1_TokenRequest(a.(*authentication.TokenRequest), b.(*authenticationv1.TokenRequest), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authenticationv1.TokenRequestSpec)(nil), (*authentication.TokenRequestSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TokenRequestSpec_To_authentication_TokenRequestSpec(a.(*authenticationv1.TokenRequestSpec), b.(*authentication.TokenRequestSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authentication.TokenRequestSpec)(nil), (*authenticationv1.TokenRequestSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_authentication_TokenRequestSpec_To_v1_TokenRequestSpec(a.(*authentication.TokenRequestSpec), b.(*authenticationv1.TokenRequestSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authenticationv1.TokenRequestStatus)(nil), (*authentication.TokenRequestStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TokenRequestStatus_To_authentication_TokenRequestStatus(a.(*authenticationv1.TokenRequestStatus), b.(*authentication.TokenRequestStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authentication.TokenRequestStatus)(nil), (*authenticationv1.TokenRequestStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_authentication_TokenRequestStatus_To_v1_TokenRequestStatus(a.(*authentication.TokenRequestStatus), b.(*authenticationv1.TokenRequestStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authenticationv1.TokenReview)(nil), (*authentication.TokenReview)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TokenReview_To_authentication_TokenReview(a.(*authenticationv1.TokenReview), b.(*authentication.TokenReview), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authentication.TokenReview)(nil), (*authenticationv1.TokenReview)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_authentication_TokenReview_To_v1_TokenReview(a.(*authentication.TokenReview), b.(*authenticationv1.TokenReview), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authenticationv1.TokenReviewSpec)(nil), (*authentication.TokenReviewSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TokenReviewSpec_To_authentication_TokenReviewSpec(a.(*authenticationv1.TokenReviewSpec), b.(*authentication.TokenReviewSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authentication.TokenReviewSpec)(nil), (*authenticationv1.TokenReviewSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_authentication_TokenReviewSpec_To_v1_TokenReviewSpec(a.(*authentication.TokenReviewSpec), b.(*authenticationv1.TokenReviewSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authenticationv1.TokenReviewStatus)(nil), (*authentication.TokenReviewStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TokenReviewStatus_To_authentication_TokenReviewStatus(a.(*authenticationv1.TokenReviewStatus), b.(*authentication.TokenReviewStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*authentication.TokenReviewStatus)(nil), (*authenticationv1.TokenReviewStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_authentication_TokenReviewStatus_To_v1_TokenReviewStatus(a.(*authentication.TokenReviewStatus), b.(*authenticationv1.TokenReviewStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*authentication.UserInfo)(nil), (*authenticationv1.UserInfo)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_authentication_UserInfo_To_v1_UserInfo(a.(*authentication.UserInfo), b.(*authenticationv1.UserInfo), scope)
	}); err != nil {
		return err
	}
	if err := s.AddConversionFunc((*authenticationv1.UserInfo)(nil), (*authentication.UserInfo)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_UserInfo_To_authentication_UserInfo(a.(*authenticationv1.UserInfo), b.(*authentication.UserInfo), scope)
	}); err != nil {
		return err
	}
	return nil
}

func autoConvert_v1_BoundObjectReference_To_authentication_BoundObjectReference(in *authenticationv1.BoundObjectReference, out *authentication.BoundObjectReference, s conversion.Scope) error {
	out.Kind = in.Kind
	out.APIVersion = in.APIVersion
	out.Name = in.Name
	out.UID = types.UID(in.UID)
	return nil
}

// Convert_v1_BoundObjectReference_To_authentication_BoundObjectReference is an autogenerated conversion function.
func Convert_v1_BoundObjectReference_To_authentication_BoundObjectReference(in *authenticationv1.BoundObjectReference, out *authentication.BoundObjectReference, s conversion.Scope) error {
	return autoConvert_v1_BoundObjectReference_To_authentication_BoundObjectReference(in, out, s)
}

func autoConvert_authentication_BoundObjectReference_To_v1_BoundObjectReference(in *authentication.BoundObjectReference, out *authenticationv1.BoundObjectReference, s conversion.Scope) error {
	out.Kind = in.Kind
	out.APIVersion = in.APIVersion
	out.Name = in.Name
	out.UID = types.UID(in.UID)
	return nil
}

// Convert_authentication_BoundObjectReference_To_v1_BoundObjectReference is an autogenerated conversion function.
func Convert_authentication_BoundObjectReference_To_v1_BoundObjectReference(in *authentication.BoundObjectReference, out *authenticationv1.BoundObjectReference, s conversion.Scope) error {
	return autoConvert_authentication_BoundObjectReference_To_v1_BoundObjectReference(in, out, s)
}

func autoConvert_v1_SelfSubjectReview_To_authentication_SelfSubjectReview(in *authenticationv1.SelfSubjectReview, out *authentication.SelfSubjectReview, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_SelfSubjectReviewStatus_To_authentication_SelfSubjectReviewStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_SelfSubjectReview_To_authentication_SelfSubjectReview is an autogenerated conversion function.
func Convert_v1_SelfSubjectReview_To_authentication_SelfSubjectReview(in *authenticationv1.SelfSubjectReview, out *authentication.SelfSubjectReview, s conversion.Scope) error {
	return autoConvert_v1_SelfSubjectReview_To_authentication_SelfSubjectReview(in, out, s)
}

func autoConvert_authentication_SelfSubjectReview_To_v1_SelfSubjectReview(in *authentication.SelfSubjectReview, out *authenticationv1.SelfSubjectReview, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_authentication_SelfSubjectReviewStatus_To_v1_SelfSubjectReviewStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_authentication_SelfSubjectReview_To_v1_SelfSubjectReview is an autogenerated conversion function.
func Convert_authentication_SelfSubjectReview_To_v1_SelfSubjectReview(in *authentication.SelfSubjectReview, out *authenticationv1.SelfSubjectReview, s conversion.Scope) error {
	return autoConvert_authentication_SelfSubjectReview_To_v1_SelfSubjectReview(in, out, s)
}

func autoConvert_v1_SelfSubjectReviewStatus_To_authentication_SelfSubjectReviewStatus(in *authenticationv1.SelfSubjectReviewStatus, out *authentication.SelfSubjectReviewStatus, s conversion.Scope) error {
	if err := Convert_v1_UserInfo_To_authentication_UserInfo(&in.UserInfo, &out.UserInfo, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_SelfSubjectReviewStatus_To_authentication_SelfSubjectReviewStatus is an autogenerated conversion function.
func Convert_v1_SelfSubjectReviewStatus_To_authentication_SelfSubjectReviewStatus(in *authenticationv1.SelfSubjectReviewStatus, out *authentication.SelfSubjectReviewStatus, s conversion.Scope) error {
	return autoConvert_v1_SelfSubjectReviewStatus_To_authentication_SelfSubjectReviewStatus(in, out, s)
}

func autoConvert_authentication_SelfSubjectReviewStatus_To_v1_SelfSubjectReviewStatus(in *authentication.SelfSubjectReviewStatus, out *authenticationv1.SelfSubjectReviewStatus, s conversion.Scope) error {
	if err := Convert_authentication_UserInfo_To_v1_UserInfo(&in.UserInfo, &out.UserInfo, s); err != nil {
		return err
	}
	return nil
}

// Convert_authentication_SelfSubjectReviewStatus_To_v1_SelfSubjectReviewStatus is an autogenerated conversion function.
func Convert_authentication_SelfSubjectReviewStatus_To_v1_SelfSubjectReviewStatus(in *authentication.SelfSubjectReviewStatus, out *authenticationv1.SelfSubjectReviewStatus, s conversion.Scope) error {
	return autoConvert_authentication_SelfSubjectReviewStatus_To_v1_SelfSubjectReviewStatus(in, out, s)
}

func autoConvert_v1_TokenRequest_To_authentication_TokenRequest(in *authenticationv1.TokenRequest, out *authentication.TokenRequest, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_TokenRequestSpec_To_authentication_TokenRequestSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_TokenRequestStatus_To_authentication_TokenRequestStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_TokenRequest_To_authentication_TokenRequest is an autogenerated conversion function.
func Convert_v1_TokenRequest_To_authentication_TokenRequest(in *authenticationv1.TokenRequest, out *authentication.TokenRequest, s conversion.Scope) error {
	return autoConvert_v1_TokenRequest_To_authentication_TokenRequest(in, out, s)
}

func autoConvert_authentication_TokenRequest_To_v1_TokenRequest(in *authentication.TokenRequest, out *authenticationv1.TokenRequest, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_authentication_TokenRequestSpec_To_v1_TokenRequestSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_authentication_TokenRequestStatus_To_v1_TokenRequestStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_authentication_TokenRequest_To_v1_TokenRequest is an autogenerated conversion function.
func Convert_authentication_TokenRequest_To_v1_TokenRequest(in *authentication.TokenRequest, out *authenticationv1.TokenRequest, s conversion.Scope) error {
	return autoConvert_authentication_TokenRequest_To_v1_TokenRequest(in, out, s)
}

func autoConvert_v1_TokenRequestSpec_To_authentication_TokenRequestSpec(in *authenticationv1.TokenRequestSpec, out *authentication.TokenRequestSpec, s conversion.Scope) error {
	out.Audiences = *(*[]string)(unsafe.Pointer(&in.Audiences))
	if err := metav1.Convert_Pointer_int64_To_int64(&in.ExpirationSeconds, &out.ExpirationSeconds, s); err != nil {
		return err
	}
	out.BoundObjectRef = (*authentication.BoundObjectReference)(unsafe.Pointer(in.BoundObjectRef))
	return nil
}

// Convert_v1_TokenRequestSpec_To_authentication_TokenRequestSpec is an autogenerated conversion function.
func Convert_v1_TokenRequestSpec_To_authentication_TokenRequestSpec(in *authenticationv1.TokenRequestSpec, out *authentication.TokenRequestSpec, s conversion.Scope) error {
	return autoConvert_v1_TokenRequestSpec_To_authentication_TokenRequestSpec(in, out, s)
}

func autoConvert_authentication_TokenRequestSpec_To_v1_TokenRequestSpec(in *authentication.TokenRequestSpec, out *authenticationv1.TokenRequestSpec, s conversion.Scope) error {
	out.Audiences = *(*[]string)(unsafe.Pointer(&in.Audiences))
	if err := metav1.Convert_int64_To_Pointer_int64(&in.ExpirationSeconds, &out.ExpirationSeconds, s); err != nil {
		return err
	}
	out.BoundObjectRef = (*authenticationv1.BoundObjectReference)(unsafe.Pointer(in.BoundObjectRef))
	return nil
}

// Convert_authentication_TokenRequestSpec_To_v1_TokenRequestSpec is an autogenerated conversion function.
func Convert_authentication_TokenRequestSpec_To_v1_TokenRequestSpec(in *authentication.TokenRequestSpec, out *authenticationv1.TokenRequestSpec, s conversion.Scope) error {
	return autoConvert_authentication_TokenRequestSpec_To_v1_TokenRequestSpec(in, out, s)
}

func autoConvert_v1_TokenRequestStatus_To_authentication_TokenRequestStatus(in *authenticationv1.TokenRequestStatus, out *authentication.TokenRequestStatus, s conversion.Scope) error {
	out.Token = in.Token
	out.ExpirationTimestamp = in.ExpirationTimestamp
	return nil
}

// Convert_v1_TokenRequestStatus_To_authentication_TokenRequestStatus is an autogenerated conversion function.
func Convert_v1_TokenRequestStatus_To_authentication_TokenRequestStatus(in *authenticationv1.TokenRequestStatus, out *authentication.TokenRequestStatus, s conversion.Scope) error {
	return autoConvert_v1_TokenRequestStatus_To_authentication_TokenRequestStatus(in, out, s)
}

func autoConvert_authentication_TokenRequestStatus_To_v1_TokenRequestStatus(in *authentication.TokenRequestStatus, out *authenticationv1.TokenRequestStatus, s conversion.Scope) error {
	out.Token = in.Token
	out.ExpirationTimestamp = in.ExpirationTimestamp
	return nil
}

// Convert_authentication_TokenRequestStatus_To_v1_TokenRequestStatus is an autogenerated conversion function.
func Convert_authentication_TokenRequestStatus_To_v1_TokenRequestStatus(in *authentication.TokenRequestStatus, out *authenticationv1.TokenRequestStatus, s conversion.Scope) error {
	return autoConvert_authentication_TokenRequestStatus_To_v1_TokenRequestStatus(in, out, s)
}

func autoConvert_v1_TokenReview_To_authentication_TokenReview(in *authenticationv1.TokenReview, out *authentication.TokenReview, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_TokenReviewSpec_To_authentication_TokenReviewSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_TokenReviewStatus_To_authentication_TokenReviewStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_TokenReview_To_authentication_TokenReview is an autogenerated conversion function.
func Convert_v1_TokenReview_To_authentication_TokenReview(in *authenticationv1.TokenReview, out *authentication.TokenReview, s conversion.Scope) error {
	return autoConvert_v1_TokenReview_To_authentication_TokenReview(in, out, s)
}

func autoConvert_authentication_TokenReview_To_v1_TokenReview(in *authentication.TokenReview, out *authenticationv1.TokenReview, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_authentication_TokenReviewSpec_To_v1_TokenReviewSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_authentication_TokenReviewStatus_To_v1_TokenReviewStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_authentication_TokenReview_To_v1_TokenReview is an autogenerated conversion function.
func Convert_authentication_TokenReview_To_v1_TokenReview(in *authentication.TokenReview, out *authenticationv1.TokenReview, s conversion.Scope) error {
	return autoConvert_authentication_TokenReview_To_v1_TokenReview(in, out, s)
}

func autoConvert_v1_TokenReviewSpec_To_authentication_TokenReviewSpec(in *authenticationv1.TokenReviewSpec, out *authentication.TokenReviewSpec, s conversion.Scope) error {
	out.Token = in.Token
	out.Audiences = *(*[]string)(unsafe.Pointer(&in.Audiences))
	return nil
}

// Convert_v1_TokenReviewSpec_To_authentication_TokenReviewSpec is an autogenerated conversion function.
func Convert_v1_TokenReviewSpec_To_authentication_TokenReviewSpec(in *authenticationv1.TokenReviewSpec, out *authentication.TokenReviewSpec, s conversion.Scope) error {
	return autoConvert_v1_TokenReviewSpec_To_authentication_TokenReviewSpec(in, out, s)
}

func autoConvert_authentication_TokenReviewSpec_To_v1_TokenReviewSpec(in *authentication.TokenReviewSpec, out *authenticationv1.TokenReviewSpec, s conversion.Scope) error {
	out.Token = in.Token
	out.Audiences = *(*[]string)(unsafe.Pointer(&in.Audiences))
	return nil
}

// Convert_authentication_TokenReviewSpec_To_v1_TokenReviewSpec is an autogenerated conversion function.
func Convert_authentication_TokenReviewSpec_To_v1_TokenReviewSpec(in *authentication.TokenReviewSpec, out *authenticationv1.TokenReviewSpec, s conversion.Scope) error {
	return autoConvert_authentication_TokenReviewSpec_To_v1_TokenReviewSpec(in, out, s)
}

func autoConvert_v1_TokenReviewStatus_To_authentication_TokenReviewStatus(in *authenticationv1.TokenReviewStatus, out *authentication.TokenReviewStatus, s conversion.Scope) error {
	out.Authenticated = in.Authenticated
	if err := Convert_v1_UserInfo_To_authentication_UserInfo(&in.User, &out.User, s); err != nil {
		return err
	}
	out.Audiences = *(*[]string)(unsafe.Pointer(&in.Audiences))
	out.Error = in.Error
	return nil
}

// Convert_v1_TokenReviewStatus_To_authentication_TokenReviewStatus is an autogenerated conversion function.
func Convert_v1_TokenReviewStatus_To_authentication_TokenReviewStatus(in *authenticationv1.TokenReviewStatus, out *authentication.TokenReviewStatus, s conversion.Scope) error {
	return autoConvert_v1_TokenReviewStatus_To_authentication_TokenReviewStatus(in, out, s)
}

func autoConvert_authentication_TokenReviewStatus_To_v1_TokenReviewStatus(in *authentication.TokenReviewStatus, out *authenticationv1.TokenReviewStatus, s conversion.Scope) error {
	out.Authenticated = in.Authenticated
	if err := Convert_authentication_UserInfo_To_v1_UserInfo(&in.User, &out.User, s); err != nil {
		return err
	}
	out.Audiences = *(*[]string)(unsafe.Pointer(&in.Audiences))
	out.Error = in.Error
	return nil
}

// Convert_authentication_TokenReviewStatus_To_v1_TokenReviewStatus is an autogenerated conversion function.
func Convert_authentication_TokenReviewStatus_To_v1_TokenReviewStatus(in *authentication.TokenReviewStatus, out *authenticationv1.TokenReviewStatus, s conversion.Scope) error {
	return autoConvert_authentication_TokenReviewStatus_To_v1_TokenReviewStatus(in, out, s)
}

func autoConvert_v1_UserInfo_To_authentication_UserInfo(in *authenticationv1.UserInfo, out *authentication.UserInfo, s conversion.Scope) error {
	out.Username = in.Username
	out.UID = in.UID
	out.Groups = *(*[]string)(unsafe.Pointer(&in.Groups))
	out.Extra = *(*map[string]authentication.ExtraValue)(unsafe.Pointer(&in.Extra))
	return nil
}

func autoConvert_authentication_UserInfo_To_v1_UserInfo(in *authentication.UserInfo, out *authenticationv1.UserInfo, s conversion.Scope) error {
	out.Username = in.Username
	out.UID = in.UID
	out.Groups = *(*[]string)(unsafe.Pointer(&in.Groups))
	out.Extra = *(*map[string]authenticationv1.ExtraValue)(unsafe.Pointer(&in.Extra))
	return nil
}
