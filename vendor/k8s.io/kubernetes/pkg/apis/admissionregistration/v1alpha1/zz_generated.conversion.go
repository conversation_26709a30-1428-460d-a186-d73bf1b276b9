//go:build !ignore_autogenerated
// +build !ignore_autogenerated

/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by conversion-gen. DO NOT EDIT.

package v1alpha1

import (
	unsafe "unsafe"

	admissionregistrationv1 "k8s.io/api/admissionregistration/v1"
	admissionregistrationv1alpha1 "k8s.io/api/admissionregistration/v1alpha1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	conversion "k8s.io/apimachinery/pkg/conversion"
	runtime "k8s.io/apimachinery/pkg/runtime"
	admissionregistration "k8s.io/kubernetes/pkg/apis/admissionregistration"
	apisadmissionregistrationv1 "k8s.io/kubernetes/pkg/apis/admissionregistration/v1"
)

func init() {
	localSchemeBuilder.Register(RegisterConversions)
}

// RegisterConversions adds conversion functions to the given scheme.
// Public to allow building arbitrary schemes.
func RegisterConversions(s *runtime.Scheme) error {
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.ApplyConfiguration)(nil), (*admissionregistration.ApplyConfiguration)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_ApplyConfiguration_To_admissionregistration_ApplyConfiguration(a.(*admissionregistrationv1alpha1.ApplyConfiguration), b.(*admissionregistration.ApplyConfiguration), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.ApplyConfiguration)(nil), (*admissionregistrationv1alpha1.ApplyConfiguration)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_ApplyConfiguration_To_v1alpha1_ApplyConfiguration(a.(*admissionregistration.ApplyConfiguration), b.(*admissionregistrationv1alpha1.ApplyConfiguration), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.AuditAnnotation)(nil), (*admissionregistration.AuditAnnotation)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_AuditAnnotation_To_admissionregistration_AuditAnnotation(a.(*admissionregistrationv1alpha1.AuditAnnotation), b.(*admissionregistration.AuditAnnotation), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.AuditAnnotation)(nil), (*admissionregistrationv1alpha1.AuditAnnotation)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_AuditAnnotation_To_v1alpha1_AuditAnnotation(a.(*admissionregistration.AuditAnnotation), b.(*admissionregistrationv1alpha1.AuditAnnotation), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.ExpressionWarning)(nil), (*admissionregistration.ExpressionWarning)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_ExpressionWarning_To_admissionregistration_ExpressionWarning(a.(*admissionregistrationv1alpha1.ExpressionWarning), b.(*admissionregistration.ExpressionWarning), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.ExpressionWarning)(nil), (*admissionregistrationv1alpha1.ExpressionWarning)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_ExpressionWarning_To_v1alpha1_ExpressionWarning(a.(*admissionregistration.ExpressionWarning), b.(*admissionregistrationv1alpha1.ExpressionWarning), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.JSONPatch)(nil), (*admissionregistration.JSONPatch)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_JSONPatch_To_admissionregistration_JSONPatch(a.(*admissionregistrationv1alpha1.JSONPatch), b.(*admissionregistration.JSONPatch), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.JSONPatch)(nil), (*admissionregistrationv1alpha1.JSONPatch)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_JSONPatch_To_v1alpha1_JSONPatch(a.(*admissionregistration.JSONPatch), b.(*admissionregistrationv1alpha1.JSONPatch), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.MatchCondition)(nil), (*admissionregistration.MatchCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_MatchCondition_To_admissionregistration_MatchCondition(a.(*admissionregistrationv1alpha1.MatchCondition), b.(*admissionregistration.MatchCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.MatchCondition)(nil), (*admissionregistrationv1alpha1.MatchCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_MatchCondition_To_v1alpha1_MatchCondition(a.(*admissionregistration.MatchCondition), b.(*admissionregistrationv1alpha1.MatchCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.MatchResources)(nil), (*admissionregistration.MatchResources)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_MatchResources_To_admissionregistration_MatchResources(a.(*admissionregistrationv1alpha1.MatchResources), b.(*admissionregistration.MatchResources), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.MatchResources)(nil), (*admissionregistrationv1alpha1.MatchResources)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_MatchResources_To_v1alpha1_MatchResources(a.(*admissionregistration.MatchResources), b.(*admissionregistrationv1alpha1.MatchResources), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.MutatingAdmissionPolicy)(nil), (*admissionregistration.MutatingAdmissionPolicy)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_MutatingAdmissionPolicy_To_admissionregistration_MutatingAdmissionPolicy(a.(*admissionregistrationv1alpha1.MutatingAdmissionPolicy), b.(*admissionregistration.MutatingAdmissionPolicy), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.MutatingAdmissionPolicy)(nil), (*admissionregistrationv1alpha1.MutatingAdmissionPolicy)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_MutatingAdmissionPolicy_To_v1alpha1_MutatingAdmissionPolicy(a.(*admissionregistration.MutatingAdmissionPolicy), b.(*admissionregistrationv1alpha1.MutatingAdmissionPolicy), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.MutatingAdmissionPolicyBinding)(nil), (*admissionregistration.MutatingAdmissionPolicyBinding)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_MutatingAdmissionPolicyBinding_To_admissionregistration_MutatingAdmissionPolicyBinding(a.(*admissionregistrationv1alpha1.MutatingAdmissionPolicyBinding), b.(*admissionregistration.MutatingAdmissionPolicyBinding), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.MutatingAdmissionPolicyBinding)(nil), (*admissionregistrationv1alpha1.MutatingAdmissionPolicyBinding)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_MutatingAdmissionPolicyBinding_To_v1alpha1_MutatingAdmissionPolicyBinding(a.(*admissionregistration.MutatingAdmissionPolicyBinding), b.(*admissionregistrationv1alpha1.MutatingAdmissionPolicyBinding), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingList)(nil), (*admissionregistration.MutatingAdmissionPolicyBindingList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_MutatingAdmissionPolicyBindingList_To_admissionregistration_MutatingAdmissionPolicyBindingList(a.(*admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingList), b.(*admissionregistration.MutatingAdmissionPolicyBindingList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.MutatingAdmissionPolicyBindingList)(nil), (*admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_MutatingAdmissionPolicyBindingList_To_v1alpha1_MutatingAdmissionPolicyBindingList(a.(*admissionregistration.MutatingAdmissionPolicyBindingList), b.(*admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingSpec)(nil), (*admissionregistration.MutatingAdmissionPolicyBindingSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_MutatingAdmissionPolicyBindingSpec_To_admissionregistration_MutatingAdmissionPolicyBindingSpec(a.(*admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingSpec), b.(*admissionregistration.MutatingAdmissionPolicyBindingSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.MutatingAdmissionPolicyBindingSpec)(nil), (*admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_MutatingAdmissionPolicyBindingSpec_To_v1alpha1_MutatingAdmissionPolicyBindingSpec(a.(*admissionregistration.MutatingAdmissionPolicyBindingSpec), b.(*admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.MutatingAdmissionPolicyList)(nil), (*admissionregistration.MutatingAdmissionPolicyList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_MutatingAdmissionPolicyList_To_admissionregistration_MutatingAdmissionPolicyList(a.(*admissionregistrationv1alpha1.MutatingAdmissionPolicyList), b.(*admissionregistration.MutatingAdmissionPolicyList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.MutatingAdmissionPolicyList)(nil), (*admissionregistrationv1alpha1.MutatingAdmissionPolicyList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_MutatingAdmissionPolicyList_To_v1alpha1_MutatingAdmissionPolicyList(a.(*admissionregistration.MutatingAdmissionPolicyList), b.(*admissionregistrationv1alpha1.MutatingAdmissionPolicyList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.MutatingAdmissionPolicySpec)(nil), (*admissionregistration.MutatingAdmissionPolicySpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_MutatingAdmissionPolicySpec_To_admissionregistration_MutatingAdmissionPolicySpec(a.(*admissionregistrationv1alpha1.MutatingAdmissionPolicySpec), b.(*admissionregistration.MutatingAdmissionPolicySpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.MutatingAdmissionPolicySpec)(nil), (*admissionregistrationv1alpha1.MutatingAdmissionPolicySpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_MutatingAdmissionPolicySpec_To_v1alpha1_MutatingAdmissionPolicySpec(a.(*admissionregistration.MutatingAdmissionPolicySpec), b.(*admissionregistrationv1alpha1.MutatingAdmissionPolicySpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.Mutation)(nil), (*admissionregistration.Mutation)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_Mutation_To_admissionregistration_Mutation(a.(*admissionregistrationv1alpha1.Mutation), b.(*admissionregistration.Mutation), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.Mutation)(nil), (*admissionregistrationv1alpha1.Mutation)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_Mutation_To_v1alpha1_Mutation(a.(*admissionregistration.Mutation), b.(*admissionregistrationv1alpha1.Mutation), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.NamedRuleWithOperations)(nil), (*admissionregistration.NamedRuleWithOperations)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_NamedRuleWithOperations_To_admissionregistration_NamedRuleWithOperations(a.(*admissionregistrationv1alpha1.NamedRuleWithOperations), b.(*admissionregistration.NamedRuleWithOperations), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.NamedRuleWithOperations)(nil), (*admissionregistrationv1alpha1.NamedRuleWithOperations)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_NamedRuleWithOperations_To_v1alpha1_NamedRuleWithOperations(a.(*admissionregistration.NamedRuleWithOperations), b.(*admissionregistrationv1alpha1.NamedRuleWithOperations), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.ParamKind)(nil), (*admissionregistration.ParamKind)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_ParamKind_To_admissionregistration_ParamKind(a.(*admissionregistrationv1alpha1.ParamKind), b.(*admissionregistration.ParamKind), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.ParamKind)(nil), (*admissionregistrationv1alpha1.ParamKind)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_ParamKind_To_v1alpha1_ParamKind(a.(*admissionregistration.ParamKind), b.(*admissionregistrationv1alpha1.ParamKind), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.ParamRef)(nil), (*admissionregistration.ParamRef)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_ParamRef_To_admissionregistration_ParamRef(a.(*admissionregistrationv1alpha1.ParamRef), b.(*admissionregistration.ParamRef), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.ParamRef)(nil), (*admissionregistrationv1alpha1.ParamRef)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_ParamRef_To_v1alpha1_ParamRef(a.(*admissionregistration.ParamRef), b.(*admissionregistrationv1alpha1.ParamRef), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.TypeChecking)(nil), (*admissionregistration.TypeChecking)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_TypeChecking_To_admissionregistration_TypeChecking(a.(*admissionregistrationv1alpha1.TypeChecking), b.(*admissionregistration.TypeChecking), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.TypeChecking)(nil), (*admissionregistrationv1alpha1.TypeChecking)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_TypeChecking_To_v1alpha1_TypeChecking(a.(*admissionregistration.TypeChecking), b.(*admissionregistrationv1alpha1.TypeChecking), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.ValidatingAdmissionPolicy)(nil), (*admissionregistration.ValidatingAdmissionPolicy)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_ValidatingAdmissionPolicy_To_admissionregistration_ValidatingAdmissionPolicy(a.(*admissionregistrationv1alpha1.ValidatingAdmissionPolicy), b.(*admissionregistration.ValidatingAdmissionPolicy), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.ValidatingAdmissionPolicy)(nil), (*admissionregistrationv1alpha1.ValidatingAdmissionPolicy)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_ValidatingAdmissionPolicy_To_v1alpha1_ValidatingAdmissionPolicy(a.(*admissionregistration.ValidatingAdmissionPolicy), b.(*admissionregistrationv1alpha1.ValidatingAdmissionPolicy), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.ValidatingAdmissionPolicyBinding)(nil), (*admissionregistration.ValidatingAdmissionPolicyBinding)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_ValidatingAdmissionPolicyBinding_To_admissionregistration_ValidatingAdmissionPolicyBinding(a.(*admissionregistrationv1alpha1.ValidatingAdmissionPolicyBinding), b.(*admissionregistration.ValidatingAdmissionPolicyBinding), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.ValidatingAdmissionPolicyBinding)(nil), (*admissionregistrationv1alpha1.ValidatingAdmissionPolicyBinding)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_ValidatingAdmissionPolicyBinding_To_v1alpha1_ValidatingAdmissionPolicyBinding(a.(*admissionregistration.ValidatingAdmissionPolicyBinding), b.(*admissionregistrationv1alpha1.ValidatingAdmissionPolicyBinding), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingList)(nil), (*admissionregistration.ValidatingAdmissionPolicyBindingList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_ValidatingAdmissionPolicyBindingList_To_admissionregistration_ValidatingAdmissionPolicyBindingList(a.(*admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingList), b.(*admissionregistration.ValidatingAdmissionPolicyBindingList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.ValidatingAdmissionPolicyBindingList)(nil), (*admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_ValidatingAdmissionPolicyBindingList_To_v1alpha1_ValidatingAdmissionPolicyBindingList(a.(*admissionregistration.ValidatingAdmissionPolicyBindingList), b.(*admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingSpec)(nil), (*admissionregistration.ValidatingAdmissionPolicyBindingSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_ValidatingAdmissionPolicyBindingSpec_To_admissionregistration_ValidatingAdmissionPolicyBindingSpec(a.(*admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingSpec), b.(*admissionregistration.ValidatingAdmissionPolicyBindingSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.ValidatingAdmissionPolicyBindingSpec)(nil), (*admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_ValidatingAdmissionPolicyBindingSpec_To_v1alpha1_ValidatingAdmissionPolicyBindingSpec(a.(*admissionregistration.ValidatingAdmissionPolicyBindingSpec), b.(*admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.ValidatingAdmissionPolicyList)(nil), (*admissionregistration.ValidatingAdmissionPolicyList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_ValidatingAdmissionPolicyList_To_admissionregistration_ValidatingAdmissionPolicyList(a.(*admissionregistrationv1alpha1.ValidatingAdmissionPolicyList), b.(*admissionregistration.ValidatingAdmissionPolicyList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.ValidatingAdmissionPolicyList)(nil), (*admissionregistrationv1alpha1.ValidatingAdmissionPolicyList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_ValidatingAdmissionPolicyList_To_v1alpha1_ValidatingAdmissionPolicyList(a.(*admissionregistration.ValidatingAdmissionPolicyList), b.(*admissionregistrationv1alpha1.ValidatingAdmissionPolicyList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.ValidatingAdmissionPolicySpec)(nil), (*admissionregistration.ValidatingAdmissionPolicySpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_ValidatingAdmissionPolicySpec_To_admissionregistration_ValidatingAdmissionPolicySpec(a.(*admissionregistrationv1alpha1.ValidatingAdmissionPolicySpec), b.(*admissionregistration.ValidatingAdmissionPolicySpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.ValidatingAdmissionPolicySpec)(nil), (*admissionregistrationv1alpha1.ValidatingAdmissionPolicySpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_ValidatingAdmissionPolicySpec_To_v1alpha1_ValidatingAdmissionPolicySpec(a.(*admissionregistration.ValidatingAdmissionPolicySpec), b.(*admissionregistrationv1alpha1.ValidatingAdmissionPolicySpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.ValidatingAdmissionPolicyStatus)(nil), (*admissionregistration.ValidatingAdmissionPolicyStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_ValidatingAdmissionPolicyStatus_To_admissionregistration_ValidatingAdmissionPolicyStatus(a.(*admissionregistrationv1alpha1.ValidatingAdmissionPolicyStatus), b.(*admissionregistration.ValidatingAdmissionPolicyStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.ValidatingAdmissionPolicyStatus)(nil), (*admissionregistrationv1alpha1.ValidatingAdmissionPolicyStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_ValidatingAdmissionPolicyStatus_To_v1alpha1_ValidatingAdmissionPolicyStatus(a.(*admissionregistration.ValidatingAdmissionPolicyStatus), b.(*admissionregistrationv1alpha1.ValidatingAdmissionPolicyStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.Validation)(nil), (*admissionregistration.Validation)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_Validation_To_admissionregistration_Validation(a.(*admissionregistrationv1alpha1.Validation), b.(*admissionregistration.Validation), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.Validation)(nil), (*admissionregistrationv1alpha1.Validation)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_Validation_To_v1alpha1_Validation(a.(*admissionregistration.Validation), b.(*admissionregistrationv1alpha1.Validation), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistrationv1alpha1.Variable)(nil), (*admissionregistration.Variable)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1alpha1_Variable_To_admissionregistration_Variable(a.(*admissionregistrationv1alpha1.Variable), b.(*admissionregistration.Variable), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*admissionregistration.Variable)(nil), (*admissionregistrationv1alpha1.Variable)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_admissionregistration_Variable_To_v1alpha1_Variable(a.(*admissionregistration.Variable), b.(*admissionregistrationv1alpha1.Variable), scope)
	}); err != nil {
		return err
	}
	return nil
}

func autoConvert_v1alpha1_ApplyConfiguration_To_admissionregistration_ApplyConfiguration(in *admissionregistrationv1alpha1.ApplyConfiguration, out *admissionregistration.ApplyConfiguration, s conversion.Scope) error {
	out.Expression = in.Expression
	return nil
}

// Convert_v1alpha1_ApplyConfiguration_To_admissionregistration_ApplyConfiguration is an autogenerated conversion function.
func Convert_v1alpha1_ApplyConfiguration_To_admissionregistration_ApplyConfiguration(in *admissionregistrationv1alpha1.ApplyConfiguration, out *admissionregistration.ApplyConfiguration, s conversion.Scope) error {
	return autoConvert_v1alpha1_ApplyConfiguration_To_admissionregistration_ApplyConfiguration(in, out, s)
}

func autoConvert_admissionregistration_ApplyConfiguration_To_v1alpha1_ApplyConfiguration(in *admissionregistration.ApplyConfiguration, out *admissionregistrationv1alpha1.ApplyConfiguration, s conversion.Scope) error {
	out.Expression = in.Expression
	return nil
}

// Convert_admissionregistration_ApplyConfiguration_To_v1alpha1_ApplyConfiguration is an autogenerated conversion function.
func Convert_admissionregistration_ApplyConfiguration_To_v1alpha1_ApplyConfiguration(in *admissionregistration.ApplyConfiguration, out *admissionregistrationv1alpha1.ApplyConfiguration, s conversion.Scope) error {
	return autoConvert_admissionregistration_ApplyConfiguration_To_v1alpha1_ApplyConfiguration(in, out, s)
}

func autoConvert_v1alpha1_AuditAnnotation_To_admissionregistration_AuditAnnotation(in *admissionregistrationv1alpha1.AuditAnnotation, out *admissionregistration.AuditAnnotation, s conversion.Scope) error {
	out.Key = in.Key
	out.ValueExpression = in.ValueExpression
	return nil
}

// Convert_v1alpha1_AuditAnnotation_To_admissionregistration_AuditAnnotation is an autogenerated conversion function.
func Convert_v1alpha1_AuditAnnotation_To_admissionregistration_AuditAnnotation(in *admissionregistrationv1alpha1.AuditAnnotation, out *admissionregistration.AuditAnnotation, s conversion.Scope) error {
	return autoConvert_v1alpha1_AuditAnnotation_To_admissionregistration_AuditAnnotation(in, out, s)
}

func autoConvert_admissionregistration_AuditAnnotation_To_v1alpha1_AuditAnnotation(in *admissionregistration.AuditAnnotation, out *admissionregistrationv1alpha1.AuditAnnotation, s conversion.Scope) error {
	out.Key = in.Key
	out.ValueExpression = in.ValueExpression
	return nil
}

// Convert_admissionregistration_AuditAnnotation_To_v1alpha1_AuditAnnotation is an autogenerated conversion function.
func Convert_admissionregistration_AuditAnnotation_To_v1alpha1_AuditAnnotation(in *admissionregistration.AuditAnnotation, out *admissionregistrationv1alpha1.AuditAnnotation, s conversion.Scope) error {
	return autoConvert_admissionregistration_AuditAnnotation_To_v1alpha1_AuditAnnotation(in, out, s)
}

func autoConvert_v1alpha1_ExpressionWarning_To_admissionregistration_ExpressionWarning(in *admissionregistrationv1alpha1.ExpressionWarning, out *admissionregistration.ExpressionWarning, s conversion.Scope) error {
	out.FieldRef = in.FieldRef
	out.Warning = in.Warning
	return nil
}

// Convert_v1alpha1_ExpressionWarning_To_admissionregistration_ExpressionWarning is an autogenerated conversion function.
func Convert_v1alpha1_ExpressionWarning_To_admissionregistration_ExpressionWarning(in *admissionregistrationv1alpha1.ExpressionWarning, out *admissionregistration.ExpressionWarning, s conversion.Scope) error {
	return autoConvert_v1alpha1_ExpressionWarning_To_admissionregistration_ExpressionWarning(in, out, s)
}

func autoConvert_admissionregistration_ExpressionWarning_To_v1alpha1_ExpressionWarning(in *admissionregistration.ExpressionWarning, out *admissionregistrationv1alpha1.ExpressionWarning, s conversion.Scope) error {
	out.FieldRef = in.FieldRef
	out.Warning = in.Warning
	return nil
}

// Convert_admissionregistration_ExpressionWarning_To_v1alpha1_ExpressionWarning is an autogenerated conversion function.
func Convert_admissionregistration_ExpressionWarning_To_v1alpha1_ExpressionWarning(in *admissionregistration.ExpressionWarning, out *admissionregistrationv1alpha1.ExpressionWarning, s conversion.Scope) error {
	return autoConvert_admissionregistration_ExpressionWarning_To_v1alpha1_ExpressionWarning(in, out, s)
}

func autoConvert_v1alpha1_JSONPatch_To_admissionregistration_JSONPatch(in *admissionregistrationv1alpha1.JSONPatch, out *admissionregistration.JSONPatch, s conversion.Scope) error {
	out.Expression = in.Expression
	return nil
}

// Convert_v1alpha1_JSONPatch_To_admissionregistration_JSONPatch is an autogenerated conversion function.
func Convert_v1alpha1_JSONPatch_To_admissionregistration_JSONPatch(in *admissionregistrationv1alpha1.JSONPatch, out *admissionregistration.JSONPatch, s conversion.Scope) error {
	return autoConvert_v1alpha1_JSONPatch_To_admissionregistration_JSONPatch(in, out, s)
}

func autoConvert_admissionregistration_JSONPatch_To_v1alpha1_JSONPatch(in *admissionregistration.JSONPatch, out *admissionregistrationv1alpha1.JSONPatch, s conversion.Scope) error {
	out.Expression = in.Expression
	return nil
}

// Convert_admissionregistration_JSONPatch_To_v1alpha1_JSONPatch is an autogenerated conversion function.
func Convert_admissionregistration_JSONPatch_To_v1alpha1_JSONPatch(in *admissionregistration.JSONPatch, out *admissionregistrationv1alpha1.JSONPatch, s conversion.Scope) error {
	return autoConvert_admissionregistration_JSONPatch_To_v1alpha1_JSONPatch(in, out, s)
}

func autoConvert_v1alpha1_MatchCondition_To_admissionregistration_MatchCondition(in *admissionregistrationv1alpha1.MatchCondition, out *admissionregistration.MatchCondition, s conversion.Scope) error {
	out.Name = in.Name
	out.Expression = in.Expression
	return nil
}

// Convert_v1alpha1_MatchCondition_To_admissionregistration_MatchCondition is an autogenerated conversion function.
func Convert_v1alpha1_MatchCondition_To_admissionregistration_MatchCondition(in *admissionregistrationv1alpha1.MatchCondition, out *admissionregistration.MatchCondition, s conversion.Scope) error {
	return autoConvert_v1alpha1_MatchCondition_To_admissionregistration_MatchCondition(in, out, s)
}

func autoConvert_admissionregistration_MatchCondition_To_v1alpha1_MatchCondition(in *admissionregistration.MatchCondition, out *admissionregistrationv1alpha1.MatchCondition, s conversion.Scope) error {
	out.Name = in.Name
	out.Expression = in.Expression
	return nil
}

// Convert_admissionregistration_MatchCondition_To_v1alpha1_MatchCondition is an autogenerated conversion function.
func Convert_admissionregistration_MatchCondition_To_v1alpha1_MatchCondition(in *admissionregistration.MatchCondition, out *admissionregistrationv1alpha1.MatchCondition, s conversion.Scope) error {
	return autoConvert_admissionregistration_MatchCondition_To_v1alpha1_MatchCondition(in, out, s)
}

func autoConvert_v1alpha1_MatchResources_To_admissionregistration_MatchResources(in *admissionregistrationv1alpha1.MatchResources, out *admissionregistration.MatchResources, s conversion.Scope) error {
	out.NamespaceSelector = (*v1.LabelSelector)(unsafe.Pointer(in.NamespaceSelector))
	out.ObjectSelector = (*v1.LabelSelector)(unsafe.Pointer(in.ObjectSelector))
	if in.ResourceRules != nil {
		in, out := &in.ResourceRules, &out.ResourceRules
		*out = make([]admissionregistration.NamedRuleWithOperations, len(*in))
		for i := range *in {
			if err := Convert_v1alpha1_NamedRuleWithOperations_To_admissionregistration_NamedRuleWithOperations(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.ResourceRules = nil
	}
	if in.ExcludeResourceRules != nil {
		in, out := &in.ExcludeResourceRules, &out.ExcludeResourceRules
		*out = make([]admissionregistration.NamedRuleWithOperations, len(*in))
		for i := range *in {
			if err := Convert_v1alpha1_NamedRuleWithOperations_To_admissionregistration_NamedRuleWithOperations(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.ExcludeResourceRules = nil
	}
	out.MatchPolicy = (*admissionregistration.MatchPolicyType)(unsafe.Pointer(in.MatchPolicy))
	return nil
}

// Convert_v1alpha1_MatchResources_To_admissionregistration_MatchResources is an autogenerated conversion function.
func Convert_v1alpha1_MatchResources_To_admissionregistration_MatchResources(in *admissionregistrationv1alpha1.MatchResources, out *admissionregistration.MatchResources, s conversion.Scope) error {
	return autoConvert_v1alpha1_MatchResources_To_admissionregistration_MatchResources(in, out, s)
}

func autoConvert_admissionregistration_MatchResources_To_v1alpha1_MatchResources(in *admissionregistration.MatchResources, out *admissionregistrationv1alpha1.MatchResources, s conversion.Scope) error {
	out.NamespaceSelector = (*v1.LabelSelector)(unsafe.Pointer(in.NamespaceSelector))
	out.ObjectSelector = (*v1.LabelSelector)(unsafe.Pointer(in.ObjectSelector))
	if in.ResourceRules != nil {
		in, out := &in.ResourceRules, &out.ResourceRules
		*out = make([]admissionregistrationv1alpha1.NamedRuleWithOperations, len(*in))
		for i := range *in {
			if err := Convert_admissionregistration_NamedRuleWithOperations_To_v1alpha1_NamedRuleWithOperations(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.ResourceRules = nil
	}
	if in.ExcludeResourceRules != nil {
		in, out := &in.ExcludeResourceRules, &out.ExcludeResourceRules
		*out = make([]admissionregistrationv1alpha1.NamedRuleWithOperations, len(*in))
		for i := range *in {
			if err := Convert_admissionregistration_NamedRuleWithOperations_To_v1alpha1_NamedRuleWithOperations(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.ExcludeResourceRules = nil
	}
	out.MatchPolicy = (*admissionregistrationv1alpha1.MatchPolicyType)(unsafe.Pointer(in.MatchPolicy))
	return nil
}

// Convert_admissionregistration_MatchResources_To_v1alpha1_MatchResources is an autogenerated conversion function.
func Convert_admissionregistration_MatchResources_To_v1alpha1_MatchResources(in *admissionregistration.MatchResources, out *admissionregistrationv1alpha1.MatchResources, s conversion.Scope) error {
	return autoConvert_admissionregistration_MatchResources_To_v1alpha1_MatchResources(in, out, s)
}

func autoConvert_v1alpha1_MutatingAdmissionPolicy_To_admissionregistration_MutatingAdmissionPolicy(in *admissionregistrationv1alpha1.MutatingAdmissionPolicy, out *admissionregistration.MutatingAdmissionPolicy, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1alpha1_MutatingAdmissionPolicySpec_To_admissionregistration_MutatingAdmissionPolicySpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1alpha1_MutatingAdmissionPolicy_To_admissionregistration_MutatingAdmissionPolicy is an autogenerated conversion function.
func Convert_v1alpha1_MutatingAdmissionPolicy_To_admissionregistration_MutatingAdmissionPolicy(in *admissionregistrationv1alpha1.MutatingAdmissionPolicy, out *admissionregistration.MutatingAdmissionPolicy, s conversion.Scope) error {
	return autoConvert_v1alpha1_MutatingAdmissionPolicy_To_admissionregistration_MutatingAdmissionPolicy(in, out, s)
}

func autoConvert_admissionregistration_MutatingAdmissionPolicy_To_v1alpha1_MutatingAdmissionPolicy(in *admissionregistration.MutatingAdmissionPolicy, out *admissionregistrationv1alpha1.MutatingAdmissionPolicy, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_admissionregistration_MutatingAdmissionPolicySpec_To_v1alpha1_MutatingAdmissionPolicySpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_admissionregistration_MutatingAdmissionPolicy_To_v1alpha1_MutatingAdmissionPolicy is an autogenerated conversion function.
func Convert_admissionregistration_MutatingAdmissionPolicy_To_v1alpha1_MutatingAdmissionPolicy(in *admissionregistration.MutatingAdmissionPolicy, out *admissionregistrationv1alpha1.MutatingAdmissionPolicy, s conversion.Scope) error {
	return autoConvert_admissionregistration_MutatingAdmissionPolicy_To_v1alpha1_MutatingAdmissionPolicy(in, out, s)
}

func autoConvert_v1alpha1_MutatingAdmissionPolicyBinding_To_admissionregistration_MutatingAdmissionPolicyBinding(in *admissionregistrationv1alpha1.MutatingAdmissionPolicyBinding, out *admissionregistration.MutatingAdmissionPolicyBinding, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1alpha1_MutatingAdmissionPolicyBindingSpec_To_admissionregistration_MutatingAdmissionPolicyBindingSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1alpha1_MutatingAdmissionPolicyBinding_To_admissionregistration_MutatingAdmissionPolicyBinding is an autogenerated conversion function.
func Convert_v1alpha1_MutatingAdmissionPolicyBinding_To_admissionregistration_MutatingAdmissionPolicyBinding(in *admissionregistrationv1alpha1.MutatingAdmissionPolicyBinding, out *admissionregistration.MutatingAdmissionPolicyBinding, s conversion.Scope) error {
	return autoConvert_v1alpha1_MutatingAdmissionPolicyBinding_To_admissionregistration_MutatingAdmissionPolicyBinding(in, out, s)
}

func autoConvert_admissionregistration_MutatingAdmissionPolicyBinding_To_v1alpha1_MutatingAdmissionPolicyBinding(in *admissionregistration.MutatingAdmissionPolicyBinding, out *admissionregistrationv1alpha1.MutatingAdmissionPolicyBinding, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_admissionregistration_MutatingAdmissionPolicyBindingSpec_To_v1alpha1_MutatingAdmissionPolicyBindingSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_admissionregistration_MutatingAdmissionPolicyBinding_To_v1alpha1_MutatingAdmissionPolicyBinding is an autogenerated conversion function.
func Convert_admissionregistration_MutatingAdmissionPolicyBinding_To_v1alpha1_MutatingAdmissionPolicyBinding(in *admissionregistration.MutatingAdmissionPolicyBinding, out *admissionregistrationv1alpha1.MutatingAdmissionPolicyBinding, s conversion.Scope) error {
	return autoConvert_admissionregistration_MutatingAdmissionPolicyBinding_To_v1alpha1_MutatingAdmissionPolicyBinding(in, out, s)
}

func autoConvert_v1alpha1_MutatingAdmissionPolicyBindingList_To_admissionregistration_MutatingAdmissionPolicyBindingList(in *admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingList, out *admissionregistration.MutatingAdmissionPolicyBindingList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]admissionregistration.MutatingAdmissionPolicyBinding, len(*in))
		for i := range *in {
			if err := Convert_v1alpha1_MutatingAdmissionPolicyBinding_To_admissionregistration_MutatingAdmissionPolicyBinding(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1alpha1_MutatingAdmissionPolicyBindingList_To_admissionregistration_MutatingAdmissionPolicyBindingList is an autogenerated conversion function.
func Convert_v1alpha1_MutatingAdmissionPolicyBindingList_To_admissionregistration_MutatingAdmissionPolicyBindingList(in *admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingList, out *admissionregistration.MutatingAdmissionPolicyBindingList, s conversion.Scope) error {
	return autoConvert_v1alpha1_MutatingAdmissionPolicyBindingList_To_admissionregistration_MutatingAdmissionPolicyBindingList(in, out, s)
}

func autoConvert_admissionregistration_MutatingAdmissionPolicyBindingList_To_v1alpha1_MutatingAdmissionPolicyBindingList(in *admissionregistration.MutatingAdmissionPolicyBindingList, out *admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]admissionregistrationv1alpha1.MutatingAdmissionPolicyBinding, len(*in))
		for i := range *in {
			if err := Convert_admissionregistration_MutatingAdmissionPolicyBinding_To_v1alpha1_MutatingAdmissionPolicyBinding(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_admissionregistration_MutatingAdmissionPolicyBindingList_To_v1alpha1_MutatingAdmissionPolicyBindingList is an autogenerated conversion function.
func Convert_admissionregistration_MutatingAdmissionPolicyBindingList_To_v1alpha1_MutatingAdmissionPolicyBindingList(in *admissionregistration.MutatingAdmissionPolicyBindingList, out *admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingList, s conversion.Scope) error {
	return autoConvert_admissionregistration_MutatingAdmissionPolicyBindingList_To_v1alpha1_MutatingAdmissionPolicyBindingList(in, out, s)
}

func autoConvert_v1alpha1_MutatingAdmissionPolicyBindingSpec_To_admissionregistration_MutatingAdmissionPolicyBindingSpec(in *admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingSpec, out *admissionregistration.MutatingAdmissionPolicyBindingSpec, s conversion.Scope) error {
	out.PolicyName = in.PolicyName
	out.ParamRef = (*admissionregistration.ParamRef)(unsafe.Pointer(in.ParamRef))
	if in.MatchResources != nil {
		in, out := &in.MatchResources, &out.MatchResources
		*out = new(admissionregistration.MatchResources)
		if err := Convert_v1alpha1_MatchResources_To_admissionregistration_MatchResources(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.MatchResources = nil
	}
	return nil
}

// Convert_v1alpha1_MutatingAdmissionPolicyBindingSpec_To_admissionregistration_MutatingAdmissionPolicyBindingSpec is an autogenerated conversion function.
func Convert_v1alpha1_MutatingAdmissionPolicyBindingSpec_To_admissionregistration_MutatingAdmissionPolicyBindingSpec(in *admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingSpec, out *admissionregistration.MutatingAdmissionPolicyBindingSpec, s conversion.Scope) error {
	return autoConvert_v1alpha1_MutatingAdmissionPolicyBindingSpec_To_admissionregistration_MutatingAdmissionPolicyBindingSpec(in, out, s)
}

func autoConvert_admissionregistration_MutatingAdmissionPolicyBindingSpec_To_v1alpha1_MutatingAdmissionPolicyBindingSpec(in *admissionregistration.MutatingAdmissionPolicyBindingSpec, out *admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingSpec, s conversion.Scope) error {
	out.PolicyName = in.PolicyName
	out.ParamRef = (*admissionregistrationv1alpha1.ParamRef)(unsafe.Pointer(in.ParamRef))
	if in.MatchResources != nil {
		in, out := &in.MatchResources, &out.MatchResources
		*out = new(admissionregistrationv1alpha1.MatchResources)
		if err := Convert_admissionregistration_MatchResources_To_v1alpha1_MatchResources(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.MatchResources = nil
	}
	return nil
}

// Convert_admissionregistration_MutatingAdmissionPolicyBindingSpec_To_v1alpha1_MutatingAdmissionPolicyBindingSpec is an autogenerated conversion function.
func Convert_admissionregistration_MutatingAdmissionPolicyBindingSpec_To_v1alpha1_MutatingAdmissionPolicyBindingSpec(in *admissionregistration.MutatingAdmissionPolicyBindingSpec, out *admissionregistrationv1alpha1.MutatingAdmissionPolicyBindingSpec, s conversion.Scope) error {
	return autoConvert_admissionregistration_MutatingAdmissionPolicyBindingSpec_To_v1alpha1_MutatingAdmissionPolicyBindingSpec(in, out, s)
}

func autoConvert_v1alpha1_MutatingAdmissionPolicyList_To_admissionregistration_MutatingAdmissionPolicyList(in *admissionregistrationv1alpha1.MutatingAdmissionPolicyList, out *admissionregistration.MutatingAdmissionPolicyList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]admissionregistration.MutatingAdmissionPolicy, len(*in))
		for i := range *in {
			if err := Convert_v1alpha1_MutatingAdmissionPolicy_To_admissionregistration_MutatingAdmissionPolicy(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1alpha1_MutatingAdmissionPolicyList_To_admissionregistration_MutatingAdmissionPolicyList is an autogenerated conversion function.
func Convert_v1alpha1_MutatingAdmissionPolicyList_To_admissionregistration_MutatingAdmissionPolicyList(in *admissionregistrationv1alpha1.MutatingAdmissionPolicyList, out *admissionregistration.MutatingAdmissionPolicyList, s conversion.Scope) error {
	return autoConvert_v1alpha1_MutatingAdmissionPolicyList_To_admissionregistration_MutatingAdmissionPolicyList(in, out, s)
}

func autoConvert_admissionregistration_MutatingAdmissionPolicyList_To_v1alpha1_MutatingAdmissionPolicyList(in *admissionregistration.MutatingAdmissionPolicyList, out *admissionregistrationv1alpha1.MutatingAdmissionPolicyList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]admissionregistrationv1alpha1.MutatingAdmissionPolicy, len(*in))
		for i := range *in {
			if err := Convert_admissionregistration_MutatingAdmissionPolicy_To_v1alpha1_MutatingAdmissionPolicy(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_admissionregistration_MutatingAdmissionPolicyList_To_v1alpha1_MutatingAdmissionPolicyList is an autogenerated conversion function.
func Convert_admissionregistration_MutatingAdmissionPolicyList_To_v1alpha1_MutatingAdmissionPolicyList(in *admissionregistration.MutatingAdmissionPolicyList, out *admissionregistrationv1alpha1.MutatingAdmissionPolicyList, s conversion.Scope) error {
	return autoConvert_admissionregistration_MutatingAdmissionPolicyList_To_v1alpha1_MutatingAdmissionPolicyList(in, out, s)
}

func autoConvert_v1alpha1_MutatingAdmissionPolicySpec_To_admissionregistration_MutatingAdmissionPolicySpec(in *admissionregistrationv1alpha1.MutatingAdmissionPolicySpec, out *admissionregistration.MutatingAdmissionPolicySpec, s conversion.Scope) error {
	out.ParamKind = (*admissionregistration.ParamKind)(unsafe.Pointer(in.ParamKind))
	if in.MatchConstraints != nil {
		in, out := &in.MatchConstraints, &out.MatchConstraints
		*out = new(admissionregistration.MatchResources)
		if err := Convert_v1alpha1_MatchResources_To_admissionregistration_MatchResources(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.MatchConstraints = nil
	}
	out.Variables = *(*[]admissionregistration.Variable)(unsafe.Pointer(&in.Variables))
	out.Mutations = *(*[]admissionregistration.Mutation)(unsafe.Pointer(&in.Mutations))
	out.FailurePolicy = (*admissionregistration.FailurePolicyType)(unsafe.Pointer(in.FailurePolicy))
	out.MatchConditions = *(*[]admissionregistration.MatchCondition)(unsafe.Pointer(&in.MatchConditions))
	out.ReinvocationPolicy = admissionregistration.ReinvocationPolicyType(in.ReinvocationPolicy)
	return nil
}

// Convert_v1alpha1_MutatingAdmissionPolicySpec_To_admissionregistration_MutatingAdmissionPolicySpec is an autogenerated conversion function.
func Convert_v1alpha1_MutatingAdmissionPolicySpec_To_admissionregistration_MutatingAdmissionPolicySpec(in *admissionregistrationv1alpha1.MutatingAdmissionPolicySpec, out *admissionregistration.MutatingAdmissionPolicySpec, s conversion.Scope) error {
	return autoConvert_v1alpha1_MutatingAdmissionPolicySpec_To_admissionregistration_MutatingAdmissionPolicySpec(in, out, s)
}

func autoConvert_admissionregistration_MutatingAdmissionPolicySpec_To_v1alpha1_MutatingAdmissionPolicySpec(in *admissionregistration.MutatingAdmissionPolicySpec, out *admissionregistrationv1alpha1.MutatingAdmissionPolicySpec, s conversion.Scope) error {
	out.ParamKind = (*admissionregistrationv1alpha1.ParamKind)(unsafe.Pointer(in.ParamKind))
	if in.MatchConstraints != nil {
		in, out := &in.MatchConstraints, &out.MatchConstraints
		*out = new(admissionregistrationv1alpha1.MatchResources)
		if err := Convert_admissionregistration_MatchResources_To_v1alpha1_MatchResources(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.MatchConstraints = nil
	}
	out.Variables = *(*[]admissionregistrationv1alpha1.Variable)(unsafe.Pointer(&in.Variables))
	out.Mutations = *(*[]admissionregistrationv1alpha1.Mutation)(unsafe.Pointer(&in.Mutations))
	out.FailurePolicy = (*admissionregistrationv1alpha1.FailurePolicyType)(unsafe.Pointer(in.FailurePolicy))
	out.MatchConditions = *(*[]admissionregistrationv1alpha1.MatchCondition)(unsafe.Pointer(&in.MatchConditions))
	out.ReinvocationPolicy = admissionregistrationv1.ReinvocationPolicyType(in.ReinvocationPolicy)
	return nil
}

// Convert_admissionregistration_MutatingAdmissionPolicySpec_To_v1alpha1_MutatingAdmissionPolicySpec is an autogenerated conversion function.
func Convert_admissionregistration_MutatingAdmissionPolicySpec_To_v1alpha1_MutatingAdmissionPolicySpec(in *admissionregistration.MutatingAdmissionPolicySpec, out *admissionregistrationv1alpha1.MutatingAdmissionPolicySpec, s conversion.Scope) error {
	return autoConvert_admissionregistration_MutatingAdmissionPolicySpec_To_v1alpha1_MutatingAdmissionPolicySpec(in, out, s)
}

func autoConvert_v1alpha1_Mutation_To_admissionregistration_Mutation(in *admissionregistrationv1alpha1.Mutation, out *admissionregistration.Mutation, s conversion.Scope) error {
	out.PatchType = admissionregistration.PatchType(in.PatchType)
	out.ApplyConfiguration = (*admissionregistration.ApplyConfiguration)(unsafe.Pointer(in.ApplyConfiguration))
	out.JSONPatch = (*admissionregistration.JSONPatch)(unsafe.Pointer(in.JSONPatch))
	return nil
}

// Convert_v1alpha1_Mutation_To_admissionregistration_Mutation is an autogenerated conversion function.
func Convert_v1alpha1_Mutation_To_admissionregistration_Mutation(in *admissionregistrationv1alpha1.Mutation, out *admissionregistration.Mutation, s conversion.Scope) error {
	return autoConvert_v1alpha1_Mutation_To_admissionregistration_Mutation(in, out, s)
}

func autoConvert_admissionregistration_Mutation_To_v1alpha1_Mutation(in *admissionregistration.Mutation, out *admissionregistrationv1alpha1.Mutation, s conversion.Scope) error {
	out.PatchType = admissionregistrationv1alpha1.PatchType(in.PatchType)
	out.ApplyConfiguration = (*admissionregistrationv1alpha1.ApplyConfiguration)(unsafe.Pointer(in.ApplyConfiguration))
	out.JSONPatch = (*admissionregistrationv1alpha1.JSONPatch)(unsafe.Pointer(in.JSONPatch))
	return nil
}

// Convert_admissionregistration_Mutation_To_v1alpha1_Mutation is an autogenerated conversion function.
func Convert_admissionregistration_Mutation_To_v1alpha1_Mutation(in *admissionregistration.Mutation, out *admissionregistrationv1alpha1.Mutation, s conversion.Scope) error {
	return autoConvert_admissionregistration_Mutation_To_v1alpha1_Mutation(in, out, s)
}

func autoConvert_v1alpha1_NamedRuleWithOperations_To_admissionregistration_NamedRuleWithOperations(in *admissionregistrationv1alpha1.NamedRuleWithOperations, out *admissionregistration.NamedRuleWithOperations, s conversion.Scope) error {
	out.ResourceNames = *(*[]string)(unsafe.Pointer(&in.ResourceNames))
	if err := apisadmissionregistrationv1.Convert_v1_RuleWithOperations_To_admissionregistration_RuleWithOperations(&in.RuleWithOperations, &out.RuleWithOperations, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1alpha1_NamedRuleWithOperations_To_admissionregistration_NamedRuleWithOperations is an autogenerated conversion function.
func Convert_v1alpha1_NamedRuleWithOperations_To_admissionregistration_NamedRuleWithOperations(in *admissionregistrationv1alpha1.NamedRuleWithOperations, out *admissionregistration.NamedRuleWithOperations, s conversion.Scope) error {
	return autoConvert_v1alpha1_NamedRuleWithOperations_To_admissionregistration_NamedRuleWithOperations(in, out, s)
}

func autoConvert_admissionregistration_NamedRuleWithOperations_To_v1alpha1_NamedRuleWithOperations(in *admissionregistration.NamedRuleWithOperations, out *admissionregistrationv1alpha1.NamedRuleWithOperations, s conversion.Scope) error {
	out.ResourceNames = *(*[]string)(unsafe.Pointer(&in.ResourceNames))
	if err := apisadmissionregistrationv1.Convert_admissionregistration_RuleWithOperations_To_v1_RuleWithOperations(&in.RuleWithOperations, &out.RuleWithOperations, s); err != nil {
		return err
	}
	return nil
}

// Convert_admissionregistration_NamedRuleWithOperations_To_v1alpha1_NamedRuleWithOperations is an autogenerated conversion function.
func Convert_admissionregistration_NamedRuleWithOperations_To_v1alpha1_NamedRuleWithOperations(in *admissionregistration.NamedRuleWithOperations, out *admissionregistrationv1alpha1.NamedRuleWithOperations, s conversion.Scope) error {
	return autoConvert_admissionregistration_NamedRuleWithOperations_To_v1alpha1_NamedRuleWithOperations(in, out, s)
}

func autoConvert_v1alpha1_ParamKind_To_admissionregistration_ParamKind(in *admissionregistrationv1alpha1.ParamKind, out *admissionregistration.ParamKind, s conversion.Scope) error {
	out.APIVersion = in.APIVersion
	out.Kind = in.Kind
	return nil
}

// Convert_v1alpha1_ParamKind_To_admissionregistration_ParamKind is an autogenerated conversion function.
func Convert_v1alpha1_ParamKind_To_admissionregistration_ParamKind(in *admissionregistrationv1alpha1.ParamKind, out *admissionregistration.ParamKind, s conversion.Scope) error {
	return autoConvert_v1alpha1_ParamKind_To_admissionregistration_ParamKind(in, out, s)
}

func autoConvert_admissionregistration_ParamKind_To_v1alpha1_ParamKind(in *admissionregistration.ParamKind, out *admissionregistrationv1alpha1.ParamKind, s conversion.Scope) error {
	out.APIVersion = in.APIVersion
	out.Kind = in.Kind
	return nil
}

// Convert_admissionregistration_ParamKind_To_v1alpha1_ParamKind is an autogenerated conversion function.
func Convert_admissionregistration_ParamKind_To_v1alpha1_ParamKind(in *admissionregistration.ParamKind, out *admissionregistrationv1alpha1.ParamKind, s conversion.Scope) error {
	return autoConvert_admissionregistration_ParamKind_To_v1alpha1_ParamKind(in, out, s)
}

func autoConvert_v1alpha1_ParamRef_To_admissionregistration_ParamRef(in *admissionregistrationv1alpha1.ParamRef, out *admissionregistration.ParamRef, s conversion.Scope) error {
	out.Name = in.Name
	out.Namespace = in.Namespace
	out.Selector = (*v1.LabelSelector)(unsafe.Pointer(in.Selector))
	out.ParameterNotFoundAction = (*admissionregistration.ParameterNotFoundActionType)(unsafe.Pointer(in.ParameterNotFoundAction))
	return nil
}

// Convert_v1alpha1_ParamRef_To_admissionregistration_ParamRef is an autogenerated conversion function.
func Convert_v1alpha1_ParamRef_To_admissionregistration_ParamRef(in *admissionregistrationv1alpha1.ParamRef, out *admissionregistration.ParamRef, s conversion.Scope) error {
	return autoConvert_v1alpha1_ParamRef_To_admissionregistration_ParamRef(in, out, s)
}

func autoConvert_admissionregistration_ParamRef_To_v1alpha1_ParamRef(in *admissionregistration.ParamRef, out *admissionregistrationv1alpha1.ParamRef, s conversion.Scope) error {
	out.Name = in.Name
	out.Namespace = in.Namespace
	out.Selector = (*v1.LabelSelector)(unsafe.Pointer(in.Selector))
	out.ParameterNotFoundAction = (*admissionregistrationv1alpha1.ParameterNotFoundActionType)(unsafe.Pointer(in.ParameterNotFoundAction))
	return nil
}

// Convert_admissionregistration_ParamRef_To_v1alpha1_ParamRef is an autogenerated conversion function.
func Convert_admissionregistration_ParamRef_To_v1alpha1_ParamRef(in *admissionregistration.ParamRef, out *admissionregistrationv1alpha1.ParamRef, s conversion.Scope) error {
	return autoConvert_admissionregistration_ParamRef_To_v1alpha1_ParamRef(in, out, s)
}

func autoConvert_v1alpha1_TypeChecking_To_admissionregistration_TypeChecking(in *admissionregistrationv1alpha1.TypeChecking, out *admissionregistration.TypeChecking, s conversion.Scope) error {
	out.ExpressionWarnings = *(*[]admissionregistration.ExpressionWarning)(unsafe.Pointer(&in.ExpressionWarnings))
	return nil
}

// Convert_v1alpha1_TypeChecking_To_admissionregistration_TypeChecking is an autogenerated conversion function.
func Convert_v1alpha1_TypeChecking_To_admissionregistration_TypeChecking(in *admissionregistrationv1alpha1.TypeChecking, out *admissionregistration.TypeChecking, s conversion.Scope) error {
	return autoConvert_v1alpha1_TypeChecking_To_admissionregistration_TypeChecking(in, out, s)
}

func autoConvert_admissionregistration_TypeChecking_To_v1alpha1_TypeChecking(in *admissionregistration.TypeChecking, out *admissionregistrationv1alpha1.TypeChecking, s conversion.Scope) error {
	out.ExpressionWarnings = *(*[]admissionregistrationv1alpha1.ExpressionWarning)(unsafe.Pointer(&in.ExpressionWarnings))
	return nil
}

// Convert_admissionregistration_TypeChecking_To_v1alpha1_TypeChecking is an autogenerated conversion function.
func Convert_admissionregistration_TypeChecking_To_v1alpha1_TypeChecking(in *admissionregistration.TypeChecking, out *admissionregistrationv1alpha1.TypeChecking, s conversion.Scope) error {
	return autoConvert_admissionregistration_TypeChecking_To_v1alpha1_TypeChecking(in, out, s)
}

func autoConvert_v1alpha1_ValidatingAdmissionPolicy_To_admissionregistration_ValidatingAdmissionPolicy(in *admissionregistrationv1alpha1.ValidatingAdmissionPolicy, out *admissionregistration.ValidatingAdmissionPolicy, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1alpha1_ValidatingAdmissionPolicySpec_To_admissionregistration_ValidatingAdmissionPolicySpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1alpha1_ValidatingAdmissionPolicyStatus_To_admissionregistration_ValidatingAdmissionPolicyStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1alpha1_ValidatingAdmissionPolicy_To_admissionregistration_ValidatingAdmissionPolicy is an autogenerated conversion function.
func Convert_v1alpha1_ValidatingAdmissionPolicy_To_admissionregistration_ValidatingAdmissionPolicy(in *admissionregistrationv1alpha1.ValidatingAdmissionPolicy, out *admissionregistration.ValidatingAdmissionPolicy, s conversion.Scope) error {
	return autoConvert_v1alpha1_ValidatingAdmissionPolicy_To_admissionregistration_ValidatingAdmissionPolicy(in, out, s)
}

func autoConvert_admissionregistration_ValidatingAdmissionPolicy_To_v1alpha1_ValidatingAdmissionPolicy(in *admissionregistration.ValidatingAdmissionPolicy, out *admissionregistrationv1alpha1.ValidatingAdmissionPolicy, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_admissionregistration_ValidatingAdmissionPolicySpec_To_v1alpha1_ValidatingAdmissionPolicySpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_admissionregistration_ValidatingAdmissionPolicyStatus_To_v1alpha1_ValidatingAdmissionPolicyStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_admissionregistration_ValidatingAdmissionPolicy_To_v1alpha1_ValidatingAdmissionPolicy is an autogenerated conversion function.
func Convert_admissionregistration_ValidatingAdmissionPolicy_To_v1alpha1_ValidatingAdmissionPolicy(in *admissionregistration.ValidatingAdmissionPolicy, out *admissionregistrationv1alpha1.ValidatingAdmissionPolicy, s conversion.Scope) error {
	return autoConvert_admissionregistration_ValidatingAdmissionPolicy_To_v1alpha1_ValidatingAdmissionPolicy(in, out, s)
}

func autoConvert_v1alpha1_ValidatingAdmissionPolicyBinding_To_admissionregistration_ValidatingAdmissionPolicyBinding(in *admissionregistrationv1alpha1.ValidatingAdmissionPolicyBinding, out *admissionregistration.ValidatingAdmissionPolicyBinding, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1alpha1_ValidatingAdmissionPolicyBindingSpec_To_admissionregistration_ValidatingAdmissionPolicyBindingSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1alpha1_ValidatingAdmissionPolicyBinding_To_admissionregistration_ValidatingAdmissionPolicyBinding is an autogenerated conversion function.
func Convert_v1alpha1_ValidatingAdmissionPolicyBinding_To_admissionregistration_ValidatingAdmissionPolicyBinding(in *admissionregistrationv1alpha1.ValidatingAdmissionPolicyBinding, out *admissionregistration.ValidatingAdmissionPolicyBinding, s conversion.Scope) error {
	return autoConvert_v1alpha1_ValidatingAdmissionPolicyBinding_To_admissionregistration_ValidatingAdmissionPolicyBinding(in, out, s)
}

func autoConvert_admissionregistration_ValidatingAdmissionPolicyBinding_To_v1alpha1_ValidatingAdmissionPolicyBinding(in *admissionregistration.ValidatingAdmissionPolicyBinding, out *admissionregistrationv1alpha1.ValidatingAdmissionPolicyBinding, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_admissionregistration_ValidatingAdmissionPolicyBindingSpec_To_v1alpha1_ValidatingAdmissionPolicyBindingSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_admissionregistration_ValidatingAdmissionPolicyBinding_To_v1alpha1_ValidatingAdmissionPolicyBinding is an autogenerated conversion function.
func Convert_admissionregistration_ValidatingAdmissionPolicyBinding_To_v1alpha1_ValidatingAdmissionPolicyBinding(in *admissionregistration.ValidatingAdmissionPolicyBinding, out *admissionregistrationv1alpha1.ValidatingAdmissionPolicyBinding, s conversion.Scope) error {
	return autoConvert_admissionregistration_ValidatingAdmissionPolicyBinding_To_v1alpha1_ValidatingAdmissionPolicyBinding(in, out, s)
}

func autoConvert_v1alpha1_ValidatingAdmissionPolicyBindingList_To_admissionregistration_ValidatingAdmissionPolicyBindingList(in *admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingList, out *admissionregistration.ValidatingAdmissionPolicyBindingList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]admissionregistration.ValidatingAdmissionPolicyBinding, len(*in))
		for i := range *in {
			if err := Convert_v1alpha1_ValidatingAdmissionPolicyBinding_To_admissionregistration_ValidatingAdmissionPolicyBinding(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1alpha1_ValidatingAdmissionPolicyBindingList_To_admissionregistration_ValidatingAdmissionPolicyBindingList is an autogenerated conversion function.
func Convert_v1alpha1_ValidatingAdmissionPolicyBindingList_To_admissionregistration_ValidatingAdmissionPolicyBindingList(in *admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingList, out *admissionregistration.ValidatingAdmissionPolicyBindingList, s conversion.Scope) error {
	return autoConvert_v1alpha1_ValidatingAdmissionPolicyBindingList_To_admissionregistration_ValidatingAdmissionPolicyBindingList(in, out, s)
}

func autoConvert_admissionregistration_ValidatingAdmissionPolicyBindingList_To_v1alpha1_ValidatingAdmissionPolicyBindingList(in *admissionregistration.ValidatingAdmissionPolicyBindingList, out *admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]admissionregistrationv1alpha1.ValidatingAdmissionPolicyBinding, len(*in))
		for i := range *in {
			if err := Convert_admissionregistration_ValidatingAdmissionPolicyBinding_To_v1alpha1_ValidatingAdmissionPolicyBinding(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_admissionregistration_ValidatingAdmissionPolicyBindingList_To_v1alpha1_ValidatingAdmissionPolicyBindingList is an autogenerated conversion function.
func Convert_admissionregistration_ValidatingAdmissionPolicyBindingList_To_v1alpha1_ValidatingAdmissionPolicyBindingList(in *admissionregistration.ValidatingAdmissionPolicyBindingList, out *admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingList, s conversion.Scope) error {
	return autoConvert_admissionregistration_ValidatingAdmissionPolicyBindingList_To_v1alpha1_ValidatingAdmissionPolicyBindingList(in, out, s)
}

func autoConvert_v1alpha1_ValidatingAdmissionPolicyBindingSpec_To_admissionregistration_ValidatingAdmissionPolicyBindingSpec(in *admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingSpec, out *admissionregistration.ValidatingAdmissionPolicyBindingSpec, s conversion.Scope) error {
	out.PolicyName = in.PolicyName
	out.ParamRef = (*admissionregistration.ParamRef)(unsafe.Pointer(in.ParamRef))
	if in.MatchResources != nil {
		in, out := &in.MatchResources, &out.MatchResources
		*out = new(admissionregistration.MatchResources)
		if err := Convert_v1alpha1_MatchResources_To_admissionregistration_MatchResources(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.MatchResources = nil
	}
	out.ValidationActions = *(*[]admissionregistration.ValidationAction)(unsafe.Pointer(&in.ValidationActions))
	return nil
}

// Convert_v1alpha1_ValidatingAdmissionPolicyBindingSpec_To_admissionregistration_ValidatingAdmissionPolicyBindingSpec is an autogenerated conversion function.
func Convert_v1alpha1_ValidatingAdmissionPolicyBindingSpec_To_admissionregistration_ValidatingAdmissionPolicyBindingSpec(in *admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingSpec, out *admissionregistration.ValidatingAdmissionPolicyBindingSpec, s conversion.Scope) error {
	return autoConvert_v1alpha1_ValidatingAdmissionPolicyBindingSpec_To_admissionregistration_ValidatingAdmissionPolicyBindingSpec(in, out, s)
}

func autoConvert_admissionregistration_ValidatingAdmissionPolicyBindingSpec_To_v1alpha1_ValidatingAdmissionPolicyBindingSpec(in *admissionregistration.ValidatingAdmissionPolicyBindingSpec, out *admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingSpec, s conversion.Scope) error {
	out.PolicyName = in.PolicyName
	out.ParamRef = (*admissionregistrationv1alpha1.ParamRef)(unsafe.Pointer(in.ParamRef))
	if in.MatchResources != nil {
		in, out := &in.MatchResources, &out.MatchResources
		*out = new(admissionregistrationv1alpha1.MatchResources)
		if err := Convert_admissionregistration_MatchResources_To_v1alpha1_MatchResources(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.MatchResources = nil
	}
	out.ValidationActions = *(*[]admissionregistrationv1alpha1.ValidationAction)(unsafe.Pointer(&in.ValidationActions))
	return nil
}

// Convert_admissionregistration_ValidatingAdmissionPolicyBindingSpec_To_v1alpha1_ValidatingAdmissionPolicyBindingSpec is an autogenerated conversion function.
func Convert_admissionregistration_ValidatingAdmissionPolicyBindingSpec_To_v1alpha1_ValidatingAdmissionPolicyBindingSpec(in *admissionregistration.ValidatingAdmissionPolicyBindingSpec, out *admissionregistrationv1alpha1.ValidatingAdmissionPolicyBindingSpec, s conversion.Scope) error {
	return autoConvert_admissionregistration_ValidatingAdmissionPolicyBindingSpec_To_v1alpha1_ValidatingAdmissionPolicyBindingSpec(in, out, s)
}

func autoConvert_v1alpha1_ValidatingAdmissionPolicyList_To_admissionregistration_ValidatingAdmissionPolicyList(in *admissionregistrationv1alpha1.ValidatingAdmissionPolicyList, out *admissionregistration.ValidatingAdmissionPolicyList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]admissionregistration.ValidatingAdmissionPolicy, len(*in))
		for i := range *in {
			if err := Convert_v1alpha1_ValidatingAdmissionPolicy_To_admissionregistration_ValidatingAdmissionPolicy(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1alpha1_ValidatingAdmissionPolicyList_To_admissionregistration_ValidatingAdmissionPolicyList is an autogenerated conversion function.
func Convert_v1alpha1_ValidatingAdmissionPolicyList_To_admissionregistration_ValidatingAdmissionPolicyList(in *admissionregistrationv1alpha1.ValidatingAdmissionPolicyList, out *admissionregistration.ValidatingAdmissionPolicyList, s conversion.Scope) error {
	return autoConvert_v1alpha1_ValidatingAdmissionPolicyList_To_admissionregistration_ValidatingAdmissionPolicyList(in, out, s)
}

func autoConvert_admissionregistration_ValidatingAdmissionPolicyList_To_v1alpha1_ValidatingAdmissionPolicyList(in *admissionregistration.ValidatingAdmissionPolicyList, out *admissionregistrationv1alpha1.ValidatingAdmissionPolicyList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]admissionregistrationv1alpha1.ValidatingAdmissionPolicy, len(*in))
		for i := range *in {
			if err := Convert_admissionregistration_ValidatingAdmissionPolicy_To_v1alpha1_ValidatingAdmissionPolicy(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_admissionregistration_ValidatingAdmissionPolicyList_To_v1alpha1_ValidatingAdmissionPolicyList is an autogenerated conversion function.
func Convert_admissionregistration_ValidatingAdmissionPolicyList_To_v1alpha1_ValidatingAdmissionPolicyList(in *admissionregistration.ValidatingAdmissionPolicyList, out *admissionregistrationv1alpha1.ValidatingAdmissionPolicyList, s conversion.Scope) error {
	return autoConvert_admissionregistration_ValidatingAdmissionPolicyList_To_v1alpha1_ValidatingAdmissionPolicyList(in, out, s)
}

func autoConvert_v1alpha1_ValidatingAdmissionPolicySpec_To_admissionregistration_ValidatingAdmissionPolicySpec(in *admissionregistrationv1alpha1.ValidatingAdmissionPolicySpec, out *admissionregistration.ValidatingAdmissionPolicySpec, s conversion.Scope) error {
	out.ParamKind = (*admissionregistration.ParamKind)(unsafe.Pointer(in.ParamKind))
	if in.MatchConstraints != nil {
		in, out := &in.MatchConstraints, &out.MatchConstraints
		*out = new(admissionregistration.MatchResources)
		if err := Convert_v1alpha1_MatchResources_To_admissionregistration_MatchResources(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.MatchConstraints = nil
	}
	out.Validations = *(*[]admissionregistration.Validation)(unsafe.Pointer(&in.Validations))
	out.FailurePolicy = (*admissionregistration.FailurePolicyType)(unsafe.Pointer(in.FailurePolicy))
	out.AuditAnnotations = *(*[]admissionregistration.AuditAnnotation)(unsafe.Pointer(&in.AuditAnnotations))
	out.MatchConditions = *(*[]admissionregistration.MatchCondition)(unsafe.Pointer(&in.MatchConditions))
	out.Variables = *(*[]admissionregistration.Variable)(unsafe.Pointer(&in.Variables))
	return nil
}

// Convert_v1alpha1_ValidatingAdmissionPolicySpec_To_admissionregistration_ValidatingAdmissionPolicySpec is an autogenerated conversion function.
func Convert_v1alpha1_ValidatingAdmissionPolicySpec_To_admissionregistration_ValidatingAdmissionPolicySpec(in *admissionregistrationv1alpha1.ValidatingAdmissionPolicySpec, out *admissionregistration.ValidatingAdmissionPolicySpec, s conversion.Scope) error {
	return autoConvert_v1alpha1_ValidatingAdmissionPolicySpec_To_admissionregistration_ValidatingAdmissionPolicySpec(in, out, s)
}

func autoConvert_admissionregistration_ValidatingAdmissionPolicySpec_To_v1alpha1_ValidatingAdmissionPolicySpec(in *admissionregistration.ValidatingAdmissionPolicySpec, out *admissionregistrationv1alpha1.ValidatingAdmissionPolicySpec, s conversion.Scope) error {
	out.ParamKind = (*admissionregistrationv1alpha1.ParamKind)(unsafe.Pointer(in.ParamKind))
	if in.MatchConstraints != nil {
		in, out := &in.MatchConstraints, &out.MatchConstraints
		*out = new(admissionregistrationv1alpha1.MatchResources)
		if err := Convert_admissionregistration_MatchResources_To_v1alpha1_MatchResources(*in, *out, s); err != nil {
			return err
		}
	} else {
		out.MatchConstraints = nil
	}
	out.Validations = *(*[]admissionregistrationv1alpha1.Validation)(unsafe.Pointer(&in.Validations))
	out.MatchConditions = *(*[]admissionregistrationv1alpha1.MatchCondition)(unsafe.Pointer(&in.MatchConditions))
	out.FailurePolicy = (*admissionregistrationv1alpha1.FailurePolicyType)(unsafe.Pointer(in.FailurePolicy))
	out.AuditAnnotations = *(*[]admissionregistrationv1alpha1.AuditAnnotation)(unsafe.Pointer(&in.AuditAnnotations))
	out.Variables = *(*[]admissionregistrationv1alpha1.Variable)(unsafe.Pointer(&in.Variables))
	return nil
}

// Convert_admissionregistration_ValidatingAdmissionPolicySpec_To_v1alpha1_ValidatingAdmissionPolicySpec is an autogenerated conversion function.
func Convert_admissionregistration_ValidatingAdmissionPolicySpec_To_v1alpha1_ValidatingAdmissionPolicySpec(in *admissionregistration.ValidatingAdmissionPolicySpec, out *admissionregistrationv1alpha1.ValidatingAdmissionPolicySpec, s conversion.Scope) error {
	return autoConvert_admissionregistration_ValidatingAdmissionPolicySpec_To_v1alpha1_ValidatingAdmissionPolicySpec(in, out, s)
}

func autoConvert_v1alpha1_ValidatingAdmissionPolicyStatus_To_admissionregistration_ValidatingAdmissionPolicyStatus(in *admissionregistrationv1alpha1.ValidatingAdmissionPolicyStatus, out *admissionregistration.ValidatingAdmissionPolicyStatus, s conversion.Scope) error {
	out.ObservedGeneration = in.ObservedGeneration
	out.TypeChecking = (*admissionregistration.TypeChecking)(unsafe.Pointer(in.TypeChecking))
	out.Conditions = *(*[]v1.Condition)(unsafe.Pointer(&in.Conditions))
	return nil
}

// Convert_v1alpha1_ValidatingAdmissionPolicyStatus_To_admissionregistration_ValidatingAdmissionPolicyStatus is an autogenerated conversion function.
func Convert_v1alpha1_ValidatingAdmissionPolicyStatus_To_admissionregistration_ValidatingAdmissionPolicyStatus(in *admissionregistrationv1alpha1.ValidatingAdmissionPolicyStatus, out *admissionregistration.ValidatingAdmissionPolicyStatus, s conversion.Scope) error {
	return autoConvert_v1alpha1_ValidatingAdmissionPolicyStatus_To_admissionregistration_ValidatingAdmissionPolicyStatus(in, out, s)
}

func autoConvert_admissionregistration_ValidatingAdmissionPolicyStatus_To_v1alpha1_ValidatingAdmissionPolicyStatus(in *admissionregistration.ValidatingAdmissionPolicyStatus, out *admissionregistrationv1alpha1.ValidatingAdmissionPolicyStatus, s conversion.Scope) error {
	out.ObservedGeneration = in.ObservedGeneration
	out.TypeChecking = (*admissionregistrationv1alpha1.TypeChecking)(unsafe.Pointer(in.TypeChecking))
	out.Conditions = *(*[]v1.Condition)(unsafe.Pointer(&in.Conditions))
	return nil
}

// Convert_admissionregistration_ValidatingAdmissionPolicyStatus_To_v1alpha1_ValidatingAdmissionPolicyStatus is an autogenerated conversion function.
func Convert_admissionregistration_ValidatingAdmissionPolicyStatus_To_v1alpha1_ValidatingAdmissionPolicyStatus(in *admissionregistration.ValidatingAdmissionPolicyStatus, out *admissionregistrationv1alpha1.ValidatingAdmissionPolicyStatus, s conversion.Scope) error {
	return autoConvert_admissionregistration_ValidatingAdmissionPolicyStatus_To_v1alpha1_ValidatingAdmissionPolicyStatus(in, out, s)
}

func autoConvert_v1alpha1_Validation_To_admissionregistration_Validation(in *admissionregistrationv1alpha1.Validation, out *admissionregistration.Validation, s conversion.Scope) error {
	out.Expression = in.Expression
	out.Message = in.Message
	out.Reason = (*v1.StatusReason)(unsafe.Pointer(in.Reason))
	out.MessageExpression = in.MessageExpression
	return nil
}

// Convert_v1alpha1_Validation_To_admissionregistration_Validation is an autogenerated conversion function.
func Convert_v1alpha1_Validation_To_admissionregistration_Validation(in *admissionregistrationv1alpha1.Validation, out *admissionregistration.Validation, s conversion.Scope) error {
	return autoConvert_v1alpha1_Validation_To_admissionregistration_Validation(in, out, s)
}

func autoConvert_admissionregistration_Validation_To_v1alpha1_Validation(in *admissionregistration.Validation, out *admissionregistrationv1alpha1.Validation, s conversion.Scope) error {
	out.Expression = in.Expression
	out.Message = in.Message
	out.Reason = (*v1.StatusReason)(unsafe.Pointer(in.Reason))
	out.MessageExpression = in.MessageExpression
	return nil
}

// Convert_admissionregistration_Validation_To_v1alpha1_Validation is an autogenerated conversion function.
func Convert_admissionregistration_Validation_To_v1alpha1_Validation(in *admissionregistration.Validation, out *admissionregistrationv1alpha1.Validation, s conversion.Scope) error {
	return autoConvert_admissionregistration_Validation_To_v1alpha1_Validation(in, out, s)
}

func autoConvert_v1alpha1_Variable_To_admissionregistration_Variable(in *admissionregistrationv1alpha1.Variable, out *admissionregistration.Variable, s conversion.Scope) error {
	out.Name = in.Name
	out.Expression = in.Expression
	return nil
}

// Convert_v1alpha1_Variable_To_admissionregistration_Variable is an autogenerated conversion function.
func Convert_v1alpha1_Variable_To_admissionregistration_Variable(in *admissionregistrationv1alpha1.Variable, out *admissionregistration.Variable, s conversion.Scope) error {
	return autoConvert_v1alpha1_Variable_To_admissionregistration_Variable(in, out, s)
}

func autoConvert_admissionregistration_Variable_To_v1alpha1_Variable(in *admissionregistration.Variable, out *admissionregistrationv1alpha1.Variable, s conversion.Scope) error {
	out.Name = in.Name
	out.Expression = in.Expression
	return nil
}

// Convert_admissionregistration_Variable_To_v1alpha1_Variable is an autogenerated conversion function.
func Convert_admissionregistration_Variable_To_v1alpha1_Variable(in *admissionregistration.Variable, out *admissionregistrationv1alpha1.Variable, s conversion.Scope) error {
	return autoConvert_admissionregistration_Variable_To_v1alpha1_Variable(in, out, s)
}
