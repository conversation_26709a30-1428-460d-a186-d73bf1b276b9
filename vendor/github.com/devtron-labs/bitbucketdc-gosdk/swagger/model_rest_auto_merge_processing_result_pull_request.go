/*
 * Bitbucket Data Center
 *
 * This is the reference document for the Atlassian Bitbucket REST API. The REST API is for developers who want to:    - integrate Bitbucket with other applications;   - create scripts that interact with Bitbucket; or   - develop plugins that enhance the Bitbucket UI, using REST to interact with the backend.    You can read more about developing Bitbucket plugins in the [Bitbucket Developer Documentation](https://developer.atlassian.com/bitbucket/server/docs/latest/).
 *
 * API version: 8.19
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package swagger

type RestAutoMergeProcessingResultPullRequest struct {
	Version         int32                        `json:"version,omitempty"`
	Open            bool                         `json:"open,omitempty"`
	Id              int64                        `json:"id,omitempty"`
	State           string                       `json:"state,omitempty"`
	Locked          bool                         `json:"locked,omitempty"`
	Closed          bool                         `json:"closed,omitempty"`
	CreatedDate     int64                        `json:"createdDate,omitempty"`
	UpdatedDate     int64                        `json:"updatedDate,omitempty"`
	HtmlDescription string                       `json:"htmlDescription,omitempty"`
	ClosedDate      int64                        `json:"closedDate,omitempty"`
	Participants    []RestPullRequestParticipant `json:"participants,omitempty"`
	Reviewers       []RestPullRequestParticipant `json:"reviewers,omitempty"`
	Description     string                       `json:"description,omitempty"`
	Title           string                       `json:"title,omitempty"`
	FromRef         *RestPullRequestFromRef      `json:"fromRef,omitempty"`
	ToRef           *RestPullRequestFromRef      `json:"toRef,omitempty"`
	Draft           bool                         `json:"draft,omitempty"`
	Links           *interface{}                 `json:"links,omitempty"`
}
