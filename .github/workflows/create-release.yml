# This workflow is used to create tag and Release for OSS

name: Create Release

# Controls when the action will run. Workflow runs when manually triggered using the UI
# or on push in charts directory of main branch.
on:
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:

  create-release-tag:
    runs-on: ubuntu-latest

    env:
      GITHUB_TOKENS: ${{ secrets.GH_SYSTEMSDT_TOKEN }}
      GIT_CONFIG_EMAIL: ${{ secrets.GH_SYSTEMSDT_EMAIL }}
      GIT_CONFIG_NAME: ${{ secrets.GH_SYSTEMSDT_USERNAME }}
      GIT_USERNAME: ${{ secrets.GH_SYSTEMSDT_USERNAME }}
      GIT_REPO: ${{ github.repository }}
      REPO: https://github.com/devtron-labs/devtron
      RELEASE_BRANCH: "main"

    steps:

      - name: Configure Git
        run: |
          echo $GITHUB_TOKENS > tokens.txt
          gh auth login --with-token < tokens.txt
          git config --global user.email "$GIT_CONFIG_EMAIL"
          git config --global user.name "$GIT_CONFIG_NAME"

      - name: Clone repository and create tag
        run: |
          mkdir preci && cd preci
          gh repo clone "$REPO"
          cd $(basename "$REPO")
          git checkout $RELEASE_BRANCH
          git pull origin $RELEASE_BRANCH
          NEXT_RELEASE_VERSION=v$(curl -s https://raw.githubusercontent.com/devtron-labs/charts/refs/heads/main/charts/devtron/Chart.yaml | grep 'appVersion' | awk '{print $2}')
          # Create and push tag
          git tag $NEXT_RELEASE_VERSION
          git push -f https://$GIT_USERNAME:$<EMAIL>/$GIT_REPO $NEXT_RELEASE_VERSION
  
  create-release:
    needs: create-release-tag
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
    # Runs series of commands to create a release
    - name: create-release
      run: |
        FILE_NAME=${FILE_NAME:=releasenotes.md}
        echo $RELEASE_TYPE
        echo $GITHUB_REPOSITORY
        echo $RELEASE_BRANCH
        version=$(curl -s https://raw.githubusercontent.com/devtron-labs/devtron/refs/heads/main/charts/devtron/Chart.yaml | grep "appVersion" | awk -F ': ' '{print $2}' )
        if [[ "$version" == *"-rc"* ]]; then
          RELEASE_TYPE="beta"
        else
          RELEASE_TYPE="minor"
        fi
        gh repo clone $GITHUB_REPOSITORY
        cd devtron
        if [[ "$RELEASE_TYPE" == "major" || "$RELEASE_TYPE" == "minor" || "$RELEASE_TYPE" == "patch" ]]
          then
            tag=$(git tag --sort=committerdate | tail -1)
            echo $tag
            echo $RELEASE_BRANCH
            echo $GITHUB_REPOSITORY
            gh release create $tag --target $RELEASE_BRANCH -t $tag -R $GITHUB_REPOSITORY -F $FILE_NAME
            git checkout -b release-bot
            git config --global user.email "$GIT_CONFIG_EMAIL"
            git config --global user.name "$GIT_CONFIG_NAME"
            rm -f $FILE_NAME
            touch $FILE_NAME
            echo "## Enhancements" > beta-releasenotes.md
            echo "## Bugs" >> beta-releasenotes.md
            echo "## Others" >> beta-releasenotes.md
            git add .
            git commit -am "Updated release-notes files"
            git push -f https://${GIT_CONFIG_NAME}:${GH_TOKEN}@github.com/${GITHUB_REPOSITORY} release-bot
        elif [[ "$RELEASE_TYPE" == "beta" ]]
          then
            git checkout -b release-bot
            git config --global user.email "$GIT_CONFIG_EMAIL"
            git config --global user.name "$GIT_CONFIG_NAME"
            echo "## Enhancements" > beta-releasenotes.md
            echo "## Bugs" >> beta-releasenotes.md
            echo "## Others" >> beta-releasenotes.md
            git add .
            git commit -am "Created release-notes files"
            git push -f https://${GIT_CONFIG_NAME}:${GH_TOKEN}@github.com/${GITHUB_REPOSITORY} release-bot
            echo "Not creating release due to beta"
        fi
      env:
        RELEASE_BRANCH: "main"
        GH_TOKEN: ${{ secrets.GH_SYSTEMSDT_TOKEN }}
        GIT_CONFIG_NAME: ${{ vars.GH_SYSTEMSDT_USERNAME }}
        GIT_CONFIG_EMAIL: ${{ secrets.GH_SYSTEMSDT_EMAIL }}
    # Send notification on discord
    - name: discord-notify
      run: |
        version=$(curl -s https://raw.githubusercontent.com/devtron-labs/devtron/refs/heads/main/charts/devtron/Chart.yaml | grep "appVersion" | awk -F ': ' '{print $2}' )
        if [[ "$version" == *"-rc"* ]]; then
          RELEASE_TYPE="beta"
        else
          RELEASE_TYPE="minor"
        fi
        if [[ "$RELEASE_TYPE" == "major" || "$RELEASE_TYPE" == "minor" || "$RELEASE_TYPE" == "patch" ]]
        then
        sudo apt install python3 python3-pip -y
        pip install discord-webhook
        export repo=$GITHUB_REPOSITORY
        export webhook_url=${{ secrets.DISCORD_WEBHOOK_URL }}
        curl -O https://raw.githubusercontent.com/pawan-59/scripts/main/python/release-note-discord.py
        ls
        python3 release-note-discord.py
        elif [[ "$RELEASE_TYPE" == "beta" ]]
        then
        echo "Not sending notification due to beta"
        fi
