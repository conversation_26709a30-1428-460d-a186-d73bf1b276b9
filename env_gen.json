[{"Category": "CD", "Fields": [{"Env": "ARGO_APP_MANUAL_SYNC_TIME", "EnvType": "int", "EnvValue": "3", "EnvDescription": "retry argocd app manual sync if the timeline is stuck in ARGOCD_SYNC_INITIATED state for more than this defined time (in mins)", "Example": "", "Deprecated": "false"}, {"Env": "CD_HELM_PIPELINE_STATUS_CRON_TIME", "EnvType": "string", "EnvValue": "*/2 * * * *", "EnvDescription": "Cron time to check the pipeline status ", "Example": "", "Deprecated": "false"}, {"Env": "CD_PIPELINE_STATUS_CRON_TIME", "EnvType": "string", "EnvValue": "*/2 * * * *", "EnvDescription": "<PERSON>ron time for CD pipeline status", "Example": "", "Deprecated": "false"}, {"Env": "CD_PIPELINE_STATUS_TIMEOUT_DURATION", "EnvType": "string", "EnvValue": "20", "EnvDescription": "Timeout for CD pipeline to get healthy", "Example": "", "Deprecated": "false"}, {"Env": "DEPLOY_STATUS_CRON_GET_PIPELINE_DEPLOYED_WITHIN_HOURS", "EnvType": "int", "EnvValue": "12", "EnvDescription": "This flag is used to fetch the deployment status of the application. It retrieves the status of deployments that occurred between 12 hours and 10 minutes prior to the current time. It fetches non-terminal statuses.", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_CHART_ARGO_CD_INSTALL_REQUEST_TIMEOUT", "EnvType": "int", "EnvValue": "1", "EnvDescription": "Context timeout for gitops concurrent async deployments", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_CHART_INSTALL_REQUEST_TIMEOUT", "EnvType": "int", "EnvValue": "6", "EnvDescription": "Context timeout for no gitops concurrent async deployments", "Example": "", "Deprecated": "false"}, {"Env": "EXPOSE_CD_METRICS", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "FEATURE_MIGRATE_ARGOCD_APPLICATION_ENABLE", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "enable migration of external argocd application to devtron pipeline", "Example": "", "Deprecated": "false"}, {"Env": "HELM_PIPELINE_STATUS_CHECK_ELIGIBLE_TIME", "EnvType": "string", "EnvValue": "120", "EnvDescription": "eligible time for checking helm app status periodically and update in db, value is in seconds., default is 120, if wfr is updated within configured time i.e. HELM_PIPELINE_STATUS_CHECK_ELIGIBLE_TIME then do not include for this cron cycle.", "Example": "", "Deprecated": "false"}, {"Env": "IS_INTERNAL_USE", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "If enabled then cd pipeline and helm apps will not need the deployment app type mandatorily. Couple this flag with HIDE_GITOPS_OR_HELM_OPTION (in Dashborad) and if gitops is configured and allowed for the env, pipeline/ helm app will gitops else no-gitops.", "Example": "", "Deprecated": "false"}, {"Env": "MIGRATE_DEPLOYMENT_CONFIG_DATA", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "migrate deployment config data from charts table to deployment_config table", "Example": "", "Deprecated": "false"}, {"Env": "PIPELINE_DEGRADED_TIME", "EnvType": "string", "EnvValue": "10", "EnvDescription": "Time to mark a pipeline degraded if not healthy in defined time", "Example": "", "Deprecated": "false"}, {"Env": "REVISION_HISTORY_LIMIT_DEVTRON_APP", "EnvType": "int", "EnvValue": "1", "EnvDescription": "Count for devtron application rivision history", "Example": "", "Deprecated": "false"}, {"Env": "REVISION_HISTORY_LIMIT_EXTERNAL_HELM_APP", "EnvType": "int", "EnvValue": "0", "EnvDescription": "Count for external helm application rivision history", "Example": "", "Deprecated": "false"}, {"Env": "REVISION_HISTORY_LIMIT_HELM_APP", "EnvType": "int", "EnvValue": "1", "EnvDescription": "To set the history limit for the helm app being deployed through devtron", "Example": "", "Deprecated": "false"}, {"Env": "REVISION_HISTORY_LIMIT_LINKED_HELM_APP", "EnvType": "int", "EnvValue": "15", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "RUN_HELM_INSTALL_IN_ASYNC_MODE_HELM_APPS", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "SHOULD_CHECK_NAMESPACE_ON_CLONE", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "should we check if namespace exists or not while cloning app", "Example": "", "Deprecated": "false"}, {"Env": "USE_DEPLOYMENT_CONFIG_DATA", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "use deployment config data from deployment_config table", "Example": "", "Deprecated": "true"}]}, {"Category": "CI_RUNNER", "Fields": [{"Env": "AZURE_ACCOUNT_KEY", "EnvType": "string", "EnvValue": "", "EnvDescription": "If blob storage is being used of azure then pass the secret key to access the bucket", "Example": "", "Deprecated": "false"}, {"Env": "AZURE_ACCOUNT_NAME", "EnvType": "string", "EnvValue": "", "EnvDescription": "Account name for azure blob storage", "Example": "", "Deprecated": "false"}, {"Env": "AZURE_BLOB_CONTAINER_CI_CACHE", "EnvType": "string", "EnvValue": "", "EnvDescription": "Cache bucket name for azure blob storage", "Example": "", "Deprecated": "false"}, {"Env": "AZURE_BLOB_CONTAINER_CI_LOG", "EnvType": "string", "EnvValue": "", "EnvDescription": "Log bucket for azure blob storage", "Example": "", "Deprecated": "false"}, {"Env": "AZURE_GATEWAY_CONNECTION_INSECURE", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "Azure gateway connection allows insecure if true", "Example": "", "Deprecated": "false"}, {"Env": "AZURE_GATEWAY_URL", "EnvType": "string", "EnvValue": "http://devtron-minio.devtroncd:9000", "EnvDescription": "<PERSON>t to CI runner for blob", "Example": "", "Deprecated": "false"}, {"Env": "BASE_LOG_LOCATION_PATH", "EnvType": "string", "EnvValue": "/home/<USER>/", "EnvDescription": "Used to store, download logs of ci workflow, artifact", "Example": "", "Deprecated": "false"}, {"Env": "BLOB_STORAGE_GCP_CREDENTIALS_JSON", "EnvType": "string", "EnvValue": "", "EnvDescription": "GCP cred json for GCS blob storage", "Example": "", "Deprecated": "false"}, {"Env": "BLOB_STORAGE_PROVIDER", "EnvType": "", "EnvValue": "S3", "EnvDescription": "Blob storage provider name(AWS/GCP/Azure)", "Example": "", "Deprecated": "false"}, {"Env": "BLOB_STORAGE_S3_ACCESS_KEY", "EnvType": "string", "EnvValue": "", "EnvDescription": "S3 access key for s3 blob storage", "Example": "", "Deprecated": "false"}, {"Env": "BLOB_STORAGE_S3_BUCKET_VERSIONED", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "To enable buctet versioning for blob storage", "Example": "", "Deprecated": "false"}, {"Env": "BLOB_STORAGE_S3_ENDPOINT", "EnvType": "string", "EnvValue": "", "EnvDescription": "S3 endpoint URL for s3 blob storage", "Example": "", "Deprecated": "false"}, {"Env": "BLOB_STORAGE_S3_ENDPOINT_INSECURE", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "To use insecure s3 endpoint", "Example": "", "Deprecated": "false"}, {"Env": "BLOB_STORAGE_S3_SECRET_KEY", "EnvType": "string", "EnvValue": "", "EnvDescription": "Secret key for s3 blob storage", "Example": "", "Deprecated": "false"}, {"Env": "BUILDX_CACHE_PATH", "EnvType": "string", "EnvValue": "/var/lib/devtron/buildx", "EnvDescription": "Path for the buildx cache", "Example": "", "Deprecated": "false"}, {"Env": "BUILDX_K8S_DRIVER_OPTIONS", "EnvType": "string", "EnvValue": "", "EnvDescription": "To enable the k8s driver and pass args for k8s driver in buildx", "Example": "", "Deprecated": "false"}, {"Env": "BUILDX_PROVENANCE_MODE", "EnvType": "string", "EnvValue": "", "EnvDescription": "provinance is set to true by default by docker. this will add some build related data in generated build manifest.it also adds some unknown:unknown key:value pair which may not be compatible by some container registries. with buildx k8s driver , provinenance=true is causing issue when push manifest to quay registry, so setting it to false", "Example": "", "Deprecated": "false"}, {"Env": "BUILD_LOG_TTL_VALUE_IN_SECS", "EnvType": "int", "EnvValue": "3600", "EnvDescription": "This is the time that the pods of ci/pre-cd/post-cd live after completion state.", "Example": "", "Deprecated": "false"}, {"Env": "CACHE_LIMIT", "EnvType": "int64", "EnvValue": "5000000000", "EnvDescription": "Cache limit.", "Example": "", "Deprecated": "false"}, {"Env": "CD_DEFAULT_ADDRESS_POOL_BASE_CIDR", "EnvType": "string", "EnvValue": "", "EnvDescription": "To pass the IP cidr for Pre/Post cd ", "Example": "", "Deprecated": "false"}, {"Env": "CD_DEFAULT_ADDRESS_POOL_SIZE", "EnvType": "int", "EnvValue": "", "EnvDescription": "The subnet size to allocate from the base pool for CD", "Example": "", "Deprecated": "false"}, {"Env": "CD_LIMIT_CI_CPU", "EnvType": "string", "EnvValue": "0.5", "EnvDescription": "CPU Resource Limit Pre/Post CD", "Example": "", "Deprecated": "false"}, {"Env": "CD_LIMIT_CI_MEM", "EnvType": "string", "EnvValue": "3G", "EnvDescription": "Memory Resource Limit Pre/Post CD", "Example": "", "Deprecated": "false"}, {"Env": "CD_NODE_LABEL_SELECTOR", "EnvType": "", "EnvValue": "", "EnvDescription": "Node label selector for  Pre/Post CD", "Example": "", "Deprecated": "false"}, {"Env": "CD_NODE_TAINTS_KEY", "EnvType": "string", "EnvValue": "dedicated", "EnvDescription": "Toleration key for Pre/Post CD", "Example": "", "Deprecated": "false"}, {"Env": "CD_NODE_TAINTS_VALUE", "EnvType": "string", "EnvValue": "ci", "EnvDescription": "Toleration value for Pre/Post CD", "Example": "", "Deprecated": "false"}, {"Env": "CD_REQ_CI_CPU", "EnvType": "string", "EnvValue": "0.5", "EnvDescription": "CPU Resource Rquest Pre/Post CD", "Example": "", "Deprecated": "false"}, {"Env": "CD_REQ_CI_MEM", "EnvType": "string", "EnvValue": "3G", "EnvDescription": "Memory Resource Rquest Pre/Post CD", "Example": "", "Deprecated": "false"}, {"Env": "CD_WORKFLOW_EXECUTOR_TYPE", "EnvType": "", "EnvValue": "AWF", "EnvDescription": "Executor type for Pre/Post CD(AWF,System)", "Example": "", "Deprecated": "false"}, {"Env": "CD_WORKFLOW_SERVICE_ACCOUNT", "EnvType": "string", "EnvValue": "cd-runner", "EnvDescription": "Service account to be used in Pre/Post CD pod", "Example": "", "Deprecated": "false"}, {"Env": "CI_DEFAULT_ADDRESS_POOL_BASE_CIDR", "EnvType": "string", "EnvValue": "", "EnvDescription": "To pass the IP cidr for CI", "Example": "", "Deprecated": "false"}, {"Env": "CI_DEFAULT_ADDRESS_POOL_SIZE", "EnvType": "int", "EnvValue": "", "EnvDescription": "The subnet size to allocate from the base pool for CI", "Example": "", "Deprecated": "false"}, {"Env": "CI_IGNORE_DOCKER_CACHE", "EnvType": "bool", "EnvValue": "", "EnvDescription": "Ignoring docker cache ", "Example": "", "Deprecated": "false"}, {"Env": "CI_LOGS_KEY_PREFIX", "EnvType": "string", "EnvValue": "", "EnvDescription": "Prefix for build logs", "Example": "", "Deprecated": "false"}, {"Env": "CI_NODE_LABEL_SELECTOR", "EnvType": "", "EnvValue": "", "EnvDescription": "Node label selector for  CI", "Example": "", "Deprecated": "false"}, {"Env": "CI_NODE_TAINTS_KEY", "EnvType": "string", "EnvValue": "", "EnvDescription": "Toleration key for CI", "Example": "", "Deprecated": "false"}, {"Env": "CI_NODE_TAINTS_VALUE", "EnvType": "string", "EnvValue": "", "EnvDescription": "Toleration value for CI", "Example": "", "Deprecated": "false"}, {"Env": "CI_RUNNER_DOCKER_MTU_VALUE", "EnvType": "int", "EnvValue": "-1", "EnvDescription": "this is to control the bytes of inofrmation passed in a network packet in ci-runner.  default is -1 (defaults to the underlying node mtu value)", "Example": "", "Deprecated": "false"}, {"Env": "CI_SUCCESS_AUTO_TRIGGER_BATCH_SIZE", "EnvType": "int", "EnvValue": "1", "EnvDescription": "this is to control the no of linked pipelines should be hanled in one go when a ci-success event of an parent ci is received", "Example": "", "Deprecated": "false"}, {"Env": "CI_VOLUME_MOUNTS_JSON", "EnvType": "string", "EnvValue": "", "EnvDescription": "additional volume mount data for CI and JOB", "Example": "", "Deprecated": "false"}, {"Env": "CI_WORKFLOW_EXECUTOR_TYPE", "EnvType": "", "EnvValue": "AWF", "EnvDescription": "Executor type for CI(AWF,System)", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_ARTIFACT_KEY_LOCATION", "EnvType": "string", "EnvValue": "arsenal-v1/ci-artifacts", "EnvDescription": "Key location for artifacts being created", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_BUILD_LOGS_BUCKET", "EnvType": "string", "EnvValue": "devtron-pro-ci-logs", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_BUILD_LOGS_KEY_PREFIX", "EnvType": "string", "EnvValue": "arsenal-v1", "EnvDescription": "Bucket prefix for build logs", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_CACHE_BUCKET", "EnvType": "string", "EnvValue": "ci-caching", "EnvDescription": "Bucket name for build cache", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_CACHE_BUCKET_REGION", "EnvType": "string", "EnvValue": "us-east-2", "EnvDescription": "Build Cache bucket region", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_CD_ARTIFACT_KEY_LOCATION", "EnvType": "string", "EnvValue": "", "EnvDescription": "Bucket prefix for build cache", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_CD_LOGS_BUCKET_REGION", "EnvType": "string", "EnvValue": "us-east-2", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_CD_NAMESPACE", "EnvType": "string", "EnvValue": "", "EnvDescription": "Namespace for devtron stack", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_CD_TIMEOUT", "EnvType": "int64", "EnvValue": "3600", "EnvDescription": "Timeout for Pre/Post-Cd to be completed", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_CI_IMAGE", "EnvType": "string", "EnvValue": "686244538589.dkr.ecr.us-east-2.amazonaws.com/cirunner:47", "EnvDescription": "To pass the ci-runner image", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_NAMESPACE", "EnvType": "string", "EnvValue": "devtron-ci", "EnvDescription": "Timeout for CI to be completed", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_TARGET_PLATFORM", "EnvType": "string", "EnvValue": "", "EnvDescription": "Default architecture for buildx", "Example": "", "Deprecated": "false"}, {"Env": "DOCKER_BUILD_CACHE_PATH", "EnvType": "string", "EnvValue": "/var/lib/docker", "EnvDescription": "Path to store cache of docker build  (/var/lib/docker-> for legacy docker build, /var/lib/devtron-> for buildx)", "Example": "", "Deprecated": "false"}, {"Env": "ENABLE_BUILD_CONTEXT", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "To Enable build context in Devtron.", "Example": "", "Deprecated": "false"}, {"Env": "ENABLE_WORKFLOW_EXECUTION_STAGE", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "if enabled then we will display build stages separately for CI/Job/Pre-Post CD", "Example": "true", "Deprecated": "false"}, {"Env": "EXTERNAL_BLOB_STORAGE_CM_NAME", "EnvType": "string", "EnvValue": "blob-storage-cm", "EnvDescription": "name of the config map(contains bucket name, etc.) in external cluster when there is some operation related to external cluster, for example:-downloading cd artifact pushed in external cluster's env and we need to download from there, downloads ci logs pushed in external cluster's blob", "Example": "", "Deprecated": "false"}, {"Env": "EXTERNAL_BLOB_STORAGE_SECRET_NAME", "EnvType": "string", "EnvValue": "blob-storage-secret", "EnvDescription": "name of the secret(contains password, accessId,passKeys, etc.) in external cluster when there is some operation related to external cluster, for example:-downloading cd artifact pushed in external cluster's env and we need to download from there, downloads ci logs pushed in external cluster's blob", "Example": "", "Deprecated": "false"}, {"Env": "EXTERNAL_CD_NODE_LABEL_SELECTOR", "EnvType": "", "EnvValue": "", "EnvDescription": "This is an array of strings used when submitting a workflow for pre or post-CD execution. If the ", "Example": "", "Deprecated": "false"}, {"Env": "EXTERNAL_CD_NODE_TAINTS_KEY", "EnvType": "string", "EnvValue": "dedicated", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "EXTERNAL_CD_NODE_TAINTS_VALUE", "EnvType": "string", "EnvValue": "ci", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "EXTERNAL_CI_API_SECRET", "EnvType": "string", "EnvValue": "devtroncd-secret", "EnvDescription": "External CI API secret.", "Example": "", "Deprecated": "false"}, {"Env": "EXTERNAL_CI_PAYLOAD", "EnvType": "string", "EnvValue": "{\"ciProjectDetails\":[{\"gitRepository\":\"https://github.com/vikram1601/getting-started-nodejs.git\",\"checkoutPath\":\"./abc\",\"commitHash\":\"239077135f8cdeeccb7857e2851348f558cb53d3\",\"commitTime\":\"2022-10-30T20:00:00\",\"branch\":\"master\",\"message\":\"Update README.md\",\"author\":\"User Name \"}],\"dockerImage\":\"445808685819.dkr.ecr.us-east-2.amazonaws.com/orch:23907713-2\"}", "EnvDescription": "External CI payload with project details.", "Example": "", "Deprecated": "false"}, {"Env": "EXTERNAL_CI_WEB_HOOK_URL", "EnvType": "string", "EnvValue": "", "EnvDescription": "default is {{HOST_URL}}/orchestrator/webhook/ext-ci. It is used for external ci.", "Example": "", "Deprecated": "false"}, {"Env": "IGNORE_CM_CS_IN_CI_JOB", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "Ignore CM/CS in CI-pipeline as Job", "Example": "", "Deprecated": "false"}, {"Env": "IMAGE_RETRY_COUNT", "EnvType": "int", "EnvValue": "0", "EnvDescription": "push artifact(image) in ci retry count ", "Example": "", "Deprecated": "false"}, {"Env": "IMAGE_RETRY_INTERVAL", "EnvType": "int", "EnvValue": "5", "EnvDescription": "image retry interval takes value in seconds", "Example": "", "Deprecated": "false"}, {"Env": "IMAGE_SCANNER_ENDPOINT", "EnvType": "string", "EnvValue": "http://image-scanner-new-demo-devtroncd-service.devtroncd:80", "EnvDescription": "Image-scanner micro-service URL", "Example": "", "Deprecated": "false"}, {"Env": "IMAGE_SCAN_MAX_RETRIES", "EnvType": "int", "EnvValue": "3", "EnvDescription": "Max retry count for image-scanning", "Example": "", "Deprecated": "false"}, {"Env": "IMAGE_SCAN_RETRY_DELAY", "EnvType": "int", "EnvValue": "5", "EnvDescription": "Delay for the image-scaning to start", "Example": "", "Deprecated": "false"}, {"Env": "IN_APP_LOGGING_ENABLED", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "Used in case of argo workflow is enabled. If enabled logs push will be managed by us, else will be managed by argo workflow.", "Example": "", "Deprecated": "false"}, {"Env": "MAX_CD_WORKFLOW_RUNNER_RETRIES", "EnvType": "int", "EnvValue": "0", "EnvDescription": "Maximum time pre/post-cd-workflow create pod if it fails to complete", "Example": "", "Deprecated": "false"}, {"Env": "MAX_CI_WORKFLOW_RETRIES", "EnvType": "int", "EnvValue": "0", "EnvDescription": "Maximum time CI-workflow create pod if it fails to complete", "Example": "", "Deprecated": "false"}, {"Env": "MODE", "EnvType": "string", "EnvValue": "DEV", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_SERVER_HOST", "EnvType": "string", "EnvValue": "localhost:4222", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "ORCH_HOST", "EnvType": "string", "EnvValue": "http://devtroncd-orchestrator-service-prod.devtroncd/webhook/msg/nats", "EnvDescription": "Orchestrator micro-service URL ", "Example": "", "Deprecated": "false"}, {"Env": "ORCH_TOKEN", "EnvType": "string", "EnvValue": "", "EnvDescription": "Orchestrator token", "Example": "", "Deprecated": "false"}, {"Env": "PRE_CI_CACHE_PATH", "EnvType": "string", "EnvValue": "/devtroncd-cache", "EnvDescription": "Cache path for Pre CI tasks", "Example": "", "Deprecated": "false"}, {"Env": "SHOW_DOCKER_BUILD_ARGS", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "To enable showing the args passed for CI in build logs", "Example": "", "Deprecated": "false"}, {"Env": "SKIP_CI_JOB_BUILD_CACHE_PUSH_PULL", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "To skip cache Push/Pull for ci job", "Example": "", "Deprecated": "false"}, {"Env": "SKIP_CREATING_ECR_REPO", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "By disabling this ECR repo won't get created if it's not available on ECR from build configuration", "Example": "", "Deprecated": "false"}, {"Env": "TERMINATION_GRACE_PERIOD_SECS", "EnvType": "int", "EnvValue": "180", "EnvDescription": "this is the time given to workflow pods to shutdown. (grace full termination time)", "Example": "", "Deprecated": "false"}, {"Env": "USE_ARTIFACT_LISTING_QUERY_V2", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "To use the V2 query for listing artifacts", "Example": "", "Deprecated": "false"}, {"Env": "USE_BLOB_STORAGE_CONFIG_IN_CD_WORKFLOW", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "To enable blob storage in pre and post cd", "Example": "", "Deprecated": "false"}, {"Env": "USE_BLOB_STORAGE_CONFIG_IN_CI_WORKFLOW", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "To enable blob storage in pre and post ci", "Example": "", "Deprecated": "false"}, {"Env": "USE_BUILDX", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "To enable buildx feature globally", "Example": "", "Deprecated": "false"}, {"Env": "USE_DOCKER_API_TO_GET_DIGEST", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "when user do not pass the digest  then this flag controls , finding the image digest using docker API or not. if set to true we get the digest from docker API call else use docker pull command. [logic in ci-runner]", "Example": "", "Deprecated": "false"}, {"Env": "USE_EXTERNAL_NODE", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "It is used in case of Pre/ Post Cd with run in application mode. If enabled the node lebels are read from EXTERNAL_CD_NODE_LABEL_SELECTOR else from CD_NODE_LABEL_SELECTOR MODE: if the vale is DEV, it will read the local kube config file or else from the cluser location.", "Example": "", "Deprecated": "false"}, {"Env": "USE_IMAGE_TAG_FROM_GIT_PROVIDER_FOR_TAG_BASED_BUILD", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "To use the same tag in container image as that of git tag", "Example": "", "Deprecated": "false"}, {"Env": "WF_CONTROLLER_INSTANCE_ID", "EnvType": "string", "EnvValue": "devtron-runner", "EnvDescription": "Workflow controller instance ID.", "Example": "", "Deprecated": "false"}, {"Env": "WORKFLOW_CACHE_CONFIG", "EnvType": "string", "EnvValue": "{}", "EnvDescription": "flag is used to configure how Docker caches are handled during a CI/CD ", "Example": "", "Deprecated": "false"}, {"Env": "WORKFLOW_SERVICE_ACCOUNT", "EnvType": "string", "EnvValue": "ci-runner", "EnvDescription": "", "Example": "", "Deprecated": "false"}]}, {"Category": "DEVTRON", "Fields": [{"Env": "-", "EnvType": "", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "ADDITIONAL_NODE_GROUP_LABELS", "EnvType": "", "EnvValue": "", "EnvDescription": "Add comma separated list of additional node group labels to default labels", "Example": "karpenter.sh/nodepool,cloud.google.com/gke-nodepool", "Deprecated": "false"}, {"Env": "APP_SYNC_IMAGE", "EnvType": "string", "EnvValue": "quay.io/devtron/chart-sync:1227622d-132-3775", "EnvDescription": "For the app sync image, this image will be used in app-manual sync job", "Example": "", "Deprecated": "false"}, {"Env": "APP_SYNC_JOB_RESOURCES_OBJ", "EnvType": "string", "EnvValue": "", "EnvDescription": "To pass the resource of app sync", "Example": "", "Deprecated": "false"}, {"Env": "APP_SYNC_SERVICE_ACCOUNT", "EnvType": "string", "EnvValue": "chart-sync", "EnvDescription": "Service account to be used in app sync Job", "Example": "", "Deprecated": "false"}, {"Env": "APP_SYNC_SHUTDOWN_WAIT_DURATION", "EnvType": "int", "EnvValue": "120", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "ARGO_AUTO_SYNC_ENABLED", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "If enabled all argocd application will have auto sync enabled", "Example": "", "Deprecated": "false"}, {"Env": "ARGO_GIT_COMMIT_RETRY_COUNT_ON_CONFLICT", "EnvType": "int", "EnvValue": "3", "EnvDescription": "retry argocd app manual sync if the timeline is stuck in ARGOCD_SYNC_INITIATED state for more than this defined time (in mins)", "Example": "", "Deprecated": "false"}, {"Env": "ARGO_GIT_COMMIT_RETRY_DELAY_ON_CONFLICT", "EnvType": "int", "EnvValue": "1", "EnvDescription": "Delay on retrying the maifest commit the on gitops", "Example": "", "Deprecated": "false"}, {"Env": "ARGO_REPO_REGISTER_RETRY_COUNT", "EnvType": "int", "EnvValue": "3", "EnvDescription": "Argo app registration in argo retries on deployment", "Example": "", "Deprecated": "false"}, {"Env": "ARGO_REPO_REGISTER_RETRY_DELAY", "EnvType": "int", "EnvValue": "10", "EnvDescription": "Argo app registration in argo cd on deployment delay between retry", "Example": "", "Deprecated": "false"}, {"Env": "ASYNC_BUILDX_CACHE_EXPORT", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "To enable async container image cache export", "Example": "", "Deprecated": "false"}, {"Env": "BATCH_SIZE", "EnvType": "int", "EnvValue": "5", "EnvDescription": "there is feature to get URL's of services/ingresses. so to extract those, we need to parse all the servcie and ingress objects of the application. this BATCH_SIZE flag controls the no of these objects get parsed in one go.", "Example": "", "Deprecated": "false"}, {"Env": "BLOB_STORAGE_ENABLED", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "BUILDX_CACHE_MODE_MIN", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "To set build cache mode to minimum in buildx", "Example": "", "Deprecated": "false"}, {"Env": "CD_HOST", "EnvType": "string", "EnvValue": "localhost", "EnvDescription": "Host for the devtron stack", "Example": "", "Deprecated": "false"}, {"Env": "CD_NAMESPACE", "EnvType": "string", "EnvValue": "devtroncd", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CD_PORT", "EnvType": "string", "EnvValue": "8000", "EnvDescription": "Port for pre/post-cd", "Example": "", "Deprecated": "false"}, {"Env": "CExpirationTime", "EnvType": "int", "EnvValue": "600", "EnvDescription": "Caching expiration time.", "Example": "", "Deprecated": "false"}, {"Env": "CI_TRIGGER_CRON_TIME", "EnvType": "int", "EnvValue": "2", "EnvDescription": "For image poll plugin", "Example": "", "Deprecated": "false"}, {"Env": "CI_WORKFLOW_STATUS_UPDATE_CRON", "EnvType": "string", "EnvValue": "*/5 * * * *", "EnvDescription": "Cron schedule for CI pipeline status", "Example": "", "Deprecated": "false"}, {"Env": "CLI_CMD_TIMEOUT_GLOBAL_SECONDS", "EnvType": "int", "EnvValue": "0", "EnvDescription": "Used in git cli opeartion timeout", "Example": "", "Deprecated": "false"}, {"Env": "CLUSTER_STATUS_CRON_TIME", "EnvType": "int", "EnvValue": "15", "EnvDescription": "Cron schedule for cluster status on resource browser", "Example": "", "Deprecated": "false"}, {"Env": "CONSUMER_CONFIG_JSON", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_LOG_TIME_LIMIT", "EnvType": "int64", "EnvValue": "1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_TIMEOUT", "EnvType": "float64", "EnvValue": "3600", "EnvDescription": "Timeout for CI to be completed", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_BOM_URL", "EnvType": "string", "EnvValue": "https://raw.githubusercontent.com/devtron-labs/devtron/%s/charts/devtron/devtron-bom.yaml", "EnvDescription": "Path to devtron-bom.yaml of devtron charts, used for module installation and devtron upgrade", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_DEFAULT_NAMESPACE", "EnvType": "string", "EnvValue": "devtroncd", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_DEX_SECRET_NAMESPACE", "EnvType": "string", "EnvValue": "devtroncd", "EnvDescription": "Namespace of dex secret", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_HELM_RELEASE_CHART_NAME", "EnvType": "string", "EnvValue": "devtron-operator", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_HELM_RELEASE_NAME", "EnvType": "string", "EnvValue": "devtron", "EnvDescription": "Name of the Devtron Helm release. ", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_HELM_RELEASE_NAMESPACE", "EnvType": "string", "EnvValue": "devtroncd", "EnvDescription": "Namespace of the Devtron Helm release", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_HELM_REPO_NAME", "EnvType": "string", "EnvValue": "devtron", "EnvDescription": "Is used to install modules (stack manager)", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_HELM_REPO_URL", "EnvType": "string", "EnvValue": "https://helm.devtron.ai", "EnvDescription": "Is used to install modules (stack manager)", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_INSTALLATION_TYPE", "EnvType": "string", "EnvValue": "", "EnvDescription": "Devtron Installation type(EA/Full)", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_INSTALLER_MODULES_PATH", "EnvType": "string", "EnvValue": "installer.modules", "EnvDescription": "Path to devtron installer modules, used to find the helm charts and values files", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_INSTALLER_RELEASE_PATH", "EnvType": "string", "EnvValue": "installer.release", "EnvDescription": "Path to devtron installer release, used to find the helm charts and values files", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_MODULES_IDENTIFIER_IN_HELM_VALUES", "EnvType": "string", "EnvValue": "installer.modules", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_OPERATOR_BASE_PATH", "EnvType": "string", "EnvValue": "", "EnvDescription": "Base path for devtron operator, used to find the helm charts and values files", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_SECRET_NAME", "EnvType": "string", "EnvValue": "devtron-secret", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_VERSION_IDENTIFIER_IN_HELM_VALUES", "EnvType": "string", "EnvValue": "installer.release", "EnvDescription": "devtron operator version identifier in helm values yaml", "Example": "", "Deprecated": "false"}, {"Env": "DEX_CID", "EnvType": "string", "EnvValue": "example-app", "EnvDescription": "dex client id ", "Example": "", "Deprecated": "false"}, {"Env": "DEX_CLIENT_ID", "EnvType": "string", "EnvValue": "argo-cd", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEX_CSTOREKEY", "EnvType": "string", "EnvValue": "", "EnvDescription": "DEX CSTOREKEY.", "Example": "", "Deprecated": "false"}, {"Env": "DEX_JWTKEY", "EnvType": "string", "EnvValue": "", "EnvDescription": "DEX JWT key.  ", "Example": "", "Deprecated": "false"}, {"Env": "DEX_RURL", "EnvType": "string", "EnvValue": "http://127.0.0.1:8080/callback", "EnvDescription": "Dex redirect URL(http://argocd-dex-server.devtroncd:8080/callback)", "Example": "", "Deprecated": "false"}, {"Env": "DEX_SCOPES", "EnvType": "", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEX_SECRET", "EnvType": "string", "EnvValue": "", "EnvDescription": "Dex secret", "Example": "", "Deprecated": "false"}, {"Env": "DEX_URL", "EnvType": "string", "EnvValue": "", "EnvDescription": "Dex service endpoint with dex path(http://argocd-dex-server.devtroncd:5556/dex)", "Example": "", "Deprecated": "false"}, {"Env": "ECR_REPO_NAME_PREFIX", "EnvType": "string", "EnvValue": "test/", "EnvDescription": "Prefix for ECR repo to be created in does not exist", "Example": "", "Deprecated": "false"}, {"Env": "ENABLE_ASYNC_ARGO_CD_INSTALL_DEVTRON_CHART", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "To enable async installation of gitops application", "Example": "", "Deprecated": "false"}, {"Env": "ENABLE_ASYNC_INSTALL_DEVTRON_CHART", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "To enable async installation of no-gitops application", "Example": "", "Deprecated": "false"}, {"Env": "ENABLE_NOTIFIER_V2", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "enable notifier v2", "Example": "", "Deprecated": "false"}, {"Env": "EPHEMERAL_SERVER_VERSION_REGEX", "EnvType": "string", "EnvValue": "v[1-9]\\.\\b(2[3-9]\\|[3-9][0-9])\\b.*", "EnvDescription": "ephemeral containers support version regex that is compared with k8sServerVersion", "Example": "", "Deprecated": "false"}, {"Env": "EVENT_URL", "EnvType": "string", "EnvValue": "http://localhost:3000/notify", "EnvDescription": "Notifier service url", "Example": "", "Deprecated": "false"}, {"Env": "EXECUTE_WIRE_NIL_CHECKER", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "checks for any nil pointer in wire.go", "Example": "", "Deprecated": "false"}, {"Env": "EXPOSE_CI_METRICS", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "To expose CI metrics", "Example": "", "Deprecated": "false"}, {"Env": "FEATURE_RESTART_WORKLOAD_BATCH_SIZE", "EnvType": "int", "EnvValue": "1", "EnvDescription": "restart workload retrieval batch size ", "Example": "", "Deprecated": "false"}, {"Env": "FEATURE_RESTART_WORKLOAD_WORKER_POOL_SIZE", "EnvType": "int", "EnvValue": "5", "EnvDescription": "restart workload retrieval pool size", "Example": "", "Deprecated": "false"}, {"Env": "FORCE_SECURITY_SCANNING", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "By enabling this no one can disable image scaning on ci-pipeline from UI", "Example": "", "Deprecated": "false"}, {"Env": "GITOPS_REPO_PREFIX", "EnvType": "string", "EnvValue": "", "EnvDescription": "Prefix for Gitops repo being creation for argocd application", "Example": "", "Deprecated": "false"}, {"Env": "GO_RUNTIME_ENV", "EnvType": "string", "EnvValue": "production", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "GRAFANA_HOST", "EnvType": "string", "EnvValue": "localhost", "EnvDescription": "Host URL for the grafana dashboard", "Example": "", "Deprecated": "false"}, {"Env": "GRAFANA_NAMESPACE", "EnvType": "string", "EnvValue": "devtroncd", "EnvDescription": "Namespace for grafana", "Example": "", "Deprecated": "false"}, {"Env": "GRAFANA_ORG_ID", "EnvType": "int", "EnvValue": "2", "EnvDescription": "Org ID for grafana for application metrics", "Example": "", "Deprecated": "false"}, {"Env": "GRAFANA_PASSWORD", "EnvType": "string", "EnvValue": "prom-operator", "EnvDescription": "Password for grafana dashboard", "Example": "", "Deprecated": "false"}, {"Env": "GRAFANA_PORT", "EnvType": "string", "EnvValue": "8090", "EnvDescription": "Port for grafana micro-service", "Example": "", "Deprecated": "false"}, {"Env": "GRAFANA_URL", "EnvType": "string", "EnvValue": "", "EnvDescription": "Host URL for the grafana dashboard", "Example": "", "Deprecated": "false"}, {"Env": "GRAFANA_USERNAME", "EnvType": "string", "EnvValue": "admin", "EnvDescription": "Username for grafana ", "Example": "", "Deprecated": "false"}, {"Env": "HIDE_IMAGE_TAGGING_HARD_DELETE", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "Flag to hide the hard delete option in the image tagging service", "Example": "", "Deprecated": "false"}, {"Env": "IGNORE_AUTOCOMPLETE_AUTH_CHECK", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "flag for ignoring auth check in autocomplete apis.", "Example": "", "Deprecated": "false"}, {"Env": "INSTALLED_MODULES", "EnvType": "", "EnvValue": "", "EnvDescription": "List of installed modules given in helm values/yaml are written in cm and used by devtron to know which modules are given", "Example": "security.trivy,security.clair", "Deprecated": "false"}, {"Env": "INSTALLER_CRD_NAMESPACE", "EnvType": "string", "EnvValue": "devtroncd", "EnvDescription": "namespace where Custom Resource Definitions get installed", "Example": "", "Deprecated": "false"}, {"Env": "INSTALLER_CRD_OBJECT_GROUP_NAME", "EnvType": "string", "EnvValue": "installer.devtron.ai", "EnvDescription": "Devtron installer CRD group name, partially deprecated.", "Example": "", "Deprecated": "false"}, {"Env": "INSTALLER_CRD_OBJECT_RESOURCE", "EnvType": "string", "EnvValue": "installers", "EnvDescription": "Devtron installer CRD resource name, partially deprecated", "Example": "", "Deprecated": "false"}, {"Env": "INSTALLER_CRD_OBJECT_VERSION", "EnvType": "string", "EnvValue": "v1alpha1", "EnvDescription": "version of the CRDs. default is v1alpha1", "Example": "", "Deprecated": "false"}, {"Env": "IS_AIR_GAP_ENVIRONMENT", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "JwtExpirationTime", "EnvType": "int", "EnvValue": "120", "EnvDescription": "JWT expiration time.", "Example": "", "Deprecated": "false"}, {"Env": "K8s_CLIENT_MAX_IDLE_CONNS_PER_HOST", "EnvType": "int", "EnvValue": "25", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_IDLE_CONN_TIMEOUT", "EnvType": "int", "EnvValue": "300", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_KEEPALIVE", "EnvType": "int", "EnvValue": "30", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_TIMEOUT", "EnvType": "int", "EnvValue": "30", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TLS_HANDSHAKE_TIMEOUT", "EnvType": "int", "EnvValue": "10", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "LENS_TIMEOUT", "EnvType": "int", "EnvValue": "0", "EnvDescription": "Lens microservice timeout.", "Example": "", "Deprecated": "false"}, {"Env": "LENS_URL", "EnvType": "string", "EnvValue": "http://lens-milandevtron-service:80", "EnvDescription": "Lens micro-service URL", "Example": "", "Deprecated": "false"}, {"Env": "LIMIT_CI_CPU", "EnvType": "string", "EnvValue": "0.5", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "LIMIT_CI_MEM", "EnvType": "string", "EnvValue": "3G", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "LOGGER_DEV_MODE", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "Enables a different logger theme.", "Example": "", "Deprecated": "false"}, {"Env": "LOG_LEVEL", "EnvType": "int", "EnvValue": "-1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "MAX_SESSION_PER_USER", "EnvType": "int", "EnvValue": "5", "EnvDescription": "max no of cluster terminal pods can be created by an user", "Example": "", "Deprecated": "false"}, {"Env": "MODULE_METADATA_API_URL", "EnvType": "string", "EnvValue": "https://api.devtron.ai/module?name=%s", "EnvDescription": "Modules list and meta info will be fetched from this server, that is central api server of devtron.", "Example": "", "Deprecated": "false"}, {"Env": "MODULE_STATUS_HANDLING_CRON_DURATION_MIN", "EnvType": "int", "EnvValue": "3", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_ACK_WAIT_IN_SECS", "EnvType": "int", "EnvValue": "120", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_BUFFER_SIZE", "EnvType": "int", "EnvValue": "-1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_MAX_AGE", "EnvType": "int", "EnvValue": "86400", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_PROCESSING_BATCH_SIZE", "EnvType": "int", "EnvValue": "1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_REPLICAS", "EnvType": "int", "EnvValue": "0", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NOTIFICATION_MEDIUM", "EnvType": "NotificationMedium", "EnvValue": "rest", "EnvDescription": "notification medium", "Example": "", "Deprecated": "false"}, {"Env": "OTEL_COLLECTOR_URL", "EnvType": "string", "EnvValue": "", "EnvDescription": "Opentelemetry URL ", "Example": "", "Deprecated": "false"}, {"Env": "PARALLELISM_LIMIT_FOR_TAG_PROCESSING", "EnvType": "int", "EnvValue": "", "EnvDescription": "App manual sync job parallel tag processing count.", "Example": "", "Deprecated": "false"}, {"Env": "PG_EXPORT_PROM_METRICS", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_ALL_FAILURE_QUERIES", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_ALL_QUERY", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_SLOW_QUERY", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_QUERY_DUR_THRESHOLD", "EnvType": "int64", "EnvValue": "5000", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PLUGIN_NAME", "EnvType": "string", "EnvValue": "Pull images from container repository", "EnvDescription": "Handles image retrieval from a container repository and triggers subsequent CI processes upon detecting new images.Current default plugin name: Pull Images from Container Repository.", "Example": "", "Deprecated": "false"}, {"Env": "PROPAGATE_EXTRA_LABELS", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "Add additional propagate labels like api.devtron.ai/appName, api.devtron.ai/envName, api.devtron.ai/project along with the user defined ones.", "Example": "", "Deprecated": "false"}, {"Env": "PROXY_SERVICE_CONFIG", "EnvType": "string", "EnvValue": "{}", "EnvDescription": "Proxy configuration for micro-service to be accessible on orhcestrator ingress", "Example": "", "Deprecated": "false"}, {"Env": "REQ_CI_CPU", "EnvType": "string", "EnvValue": "0.5", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "REQ_CI_MEM", "EnvType": "string", "EnvValue": "3G", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "RESTRICT_TERMINAL_ACCESS_FOR_NON_SUPER_USER", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "To restrict the cluster terminal from user having non-super admin acceess", "Example": "", "Deprecated": "false"}, {"Env": "RUNTIME_CONFIG_LOCAL_DEV", "EnvType": "LocalDevMode", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "SCOPED_VARIABLE_ENABLED", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "To enable scoped variable option", "Example": "", "Deprecated": "false"}, {"Env": "SCOPED_VARIABLE_FORMAT", "EnvType": "string", "EnvValue": "@{{%s}}", "EnvDescription": "Its a scope format for varialbe name.", "Example": "", "Deprecated": "false"}, {"Env": "SCOPED_VARIABLE_HANDLE_PRIMITIVES", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "This describe should we handle primitives or not in scoped variable template parsing.", "Example": "", "Deprecated": "false"}, {"Env": "SCOPED_VARIABLE_NAME_REGEX", "EnvType": "string", "EnvValue": "^[a-zA-Z][a-zA-Z0-9_-]{0,62}[a-zA-Z0-9]$", "EnvDescription": "Regex for scoped variable name that must passed this regex.", "Example": "", "Deprecated": "false"}, {"Env": "SOCKET_DISCONNECT_DELAY_SECONDS", "EnvType": "int", "EnvValue": "5", "EnvDescription": "The server closes a session when a client receiving connection have not been seen for a while.This delay is configured by this setting. By default the session is closed when a receiving connection wasn't seen for 5 seconds.", "Example": "", "Deprecated": "false"}, {"Env": "SOCKET_HEARTBEAT_SECONDS", "EnvType": "int", "EnvValue": "25", "EnvDescription": "In order to keep proxies and load balancers from closing long running http requests we need to pretend that the connection is active and send a heartbeat packet once in a while. This setting controls how often this is done. By default a heartbeat packet is sent every 25 seconds.", "Example": "", "Deprecated": "false"}, {"Env": "STREAM_CONFIG_JSON", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "SYSTEM_VAR_PREFIX", "EnvType": "string", "EnvValue": "DEVTRON_", "EnvDescription": "Scoped variable prefix, variable name must have this prefix.", "Example": "", "Deprecated": "false"}, {"Env": "TERMINAL_POD_DEFAULT_NAMESPACE", "EnvType": "string", "EnvValue": "default", "EnvDescription": "Cluster terminal default namespace", "Example": "", "Deprecated": "false"}, {"Env": "TERMINAL_POD_INACTIVE_DURATION_IN_MINS", "EnvType": "int", "EnvValue": "10", "EnvDescription": "Timeout for cluster terminal to be inactive", "Example": "", "Deprecated": "false"}, {"Env": "TERMINAL_POD_STATUS_SYNC_In_SECS", "EnvType": "int", "EnvValue": "600", "EnvDescription": "this is the time interval at which the status of the cluster terminal pod", "Example": "", "Deprecated": "false"}, {"Env": "TEST_APP", "EnvType": "string", "EnvValue": "orchestrator", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "TEST_PG_ADDR", "EnvType": "string", "EnvValue": "127.0.0.1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "TEST_PG_DATABASE", "EnvType": "string", "EnvValue": "orchestrator", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "TEST_PG_LOG_QUERY", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "TEST_PG_PASSWORD", "EnvType": "string", "EnvValue": "postgrespw", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "TEST_PG_PORT", "EnvType": "string", "EnvValue": "55000", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "TEST_PG_USER", "EnvType": "string", "EnvValue": "postgres", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "TIMEOUT_FOR_FAILED_CI_BUILD", "EnvType": "string", "EnvValue": "15", "EnvDescription": "Timeout for Failed CI build ", "Example": "", "Deprecated": "false"}, {"Env": "TIMEOUT_IN_SECONDS", "EnvType": "int", "EnvValue": "5", "EnvDescription": "timeout to compute the urls from services and ingress objects of an application", "Example": "", "Deprecated": "false"}, {"Env": "USER_SESSION_DURATION_SECONDS", "EnvType": "int", "EnvValue": "86400", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "USE_ARTIFACT_LISTING_API_V2", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "To use the V2 API for listing artifacts in Listing the images in pipeline", "Example": "", "Deprecated": "false"}, {"Env": "USE_CUSTOM_HTTP_TRANSPORT", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "USE_GIT_CLI", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "To enable git cli", "Example": "", "Deprecated": "false"}, {"Env": "USE_RBAC_CREATION_V2", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "To use the V2 for RBAC creation", "Example": "", "Deprecated": "false"}, {"Env": "VARIABLE_CACHE_ENABLED", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "This is used to  control caching of all the scope variables defined in the system.", "Example": "", "Deprecated": "false"}, {"Env": "VARIABLE_EXPRESSION_REGEX", "EnvType": "string", "EnvValue": "@{{([^}]+)}}", "EnvDescription": "Scoped variable expression regex", "Example": "", "Deprecated": "false"}, {"Env": "WEBHOOK_TOKEN", "EnvType": "string", "EnvValue": "", "EnvDescription": "If you want to continue using jenkins for CI then please provide this for authentication of requests", "Example": "", "Deprecated": "false"}]}, {"Category": "GITOPS", "Fields": [{"Env": "ACD_CM", "EnvType": "string", "EnvValue": "argocd-cm", "EnvDescription": "Name of the argocd CM", "Example": "", "Deprecated": "false"}, {"Env": "ACD_NAMESPACE", "EnvType": "string", "EnvValue": "devtroncd", "EnvDescription": "To pass the argocd namespace", "Example": "", "Deprecated": "false"}, {"Env": "ACD_PASSWORD", "EnvType": "string", "EnvValue": "", "EnvDescription": "Password for the Argocd (deprecated)", "Example": "", "Deprecated": "false"}, {"Env": "ACD_USERNAME", "EnvType": "string", "EnvValue": "admin", "EnvDescription": "User name for argocd", "Example": "", "Deprecated": "false"}, {"Env": "GITOPS_SECRET_NAME", "EnvType": "string", "EnvValue": "devtron-gitops-secret", "EnvDescription": "devtron-gitops-secret", "Example": "", "Deprecated": "false"}, {"Env": "RESOURCE_LIST_FOR_REPLICAS", "EnvType": "string", "EnvValue": "Deployment,Rollout,StatefulSet,ReplicaSet", "EnvDescription": "this holds the list of k8s resource names which support replicas key. this list used in hibernate/un hibernate process", "Example": "", "Deprecated": "false"}, {"Env": "RESOURCE_LIST_FOR_REPLICAS_BATCH_SIZE", "EnvType": "int", "EnvValue": "5", "EnvDescription": "this the batch size to control no of above resources can be parsed in one go to determine hibernate status", "Example": "", "Deprecated": "false"}]}, {"Category": "INFRA_SETUP", "Fields": [{"Env": "DASHBOARD_HOST", "EnvType": "string", "EnvValue": "localhost", "EnvDescription": "Dashboard micro-service URL", "Example": "", "Deprecated": "false"}, {"Env": "DASHBOARD_NAMESPACE", "EnvType": "string", "EnvValue": "devtroncd", "EnvDescription": "Dashboard micro-service namespace", "Example": "", "Deprecated": "false"}, {"Env": "DASHBOARD_PORT", "EnvType": "string", "EnvValue": "3000", "EnvDescription": "Port for dashboard micro-service", "Example": "", "Deprecated": "false"}, {"Env": "DEX_HOST", "EnvType": "string", "EnvValue": "http://localhost", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEX_PORT", "EnvType": "string", "EnvValue": "5556", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "GIT_SENSOR_PROTOCOL", "EnvType": "string", "EnvValue": "REST", "EnvDescription": "Protocol to connect with git-sensor micro-service", "Example": "", "Deprecated": "false"}, {"Env": "GIT_SENSOR_SERVICE_CONFIG", "EnvType": "string", "EnvValue": "{\"loadBalancingPolicy\":\"pick_first\"}", "EnvDescription": "git-sensor grpc service config", "Example": "", "Deprecated": "false"}, {"Env": "GIT_SENSOR_TIMEOUT", "EnvType": "int", "EnvValue": "0", "EnvDescription": "Timeout for getting response from the git-sensor", "Example": "", "Deprecated": "false"}, {"Env": "GIT_SENSOR_URL", "EnvType": "string", "EnvValue": "127.0.0.1:7070", "EnvDescription": "git-sensor micro-service url ", "Example": "", "Deprecated": "false"}, {"Env": "HELM_CLIENT_URL", "EnvType": "string", "EnvValue": "127.0.0.1:50051", "EnvDescription": "Kubelink micro-service url ", "Example": "", "Deprecated": "false"}, {"Env": "KUBELINK_GRPC_MAX_RECEIVE_MSG_SIZE", "EnvType": "int", "EnvValue": "20", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "KUBELINK_GRPC_MAX_SEND_MSG_SIZE", "EnvType": "int", "EnvValue": "4", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "KUBELINK_GRPC_SERVICE_CONFIG", "EnvType": "string", "EnvValue": "{\"loadBalancingPolicy\":\"round_robin\"}", "EnvDescription": "kubelink grpc service config", "Example": "", "Deprecated": "false"}]}, {"Category": "POSTGRES", "Fields": [{"Env": "APP", "EnvType": "string", "EnvValue": "orchestrator", "EnvDescription": "Application name", "Example": "", "Deprecated": "false"}, {"Env": "CASBIN_DATABASE", "EnvType": "string", "EnvValue": "casbin", "EnvDescription": "Database for casbin", "Example": "", "Deprecated": "false"}, {"Env": "PG_ADDR", "EnvType": "string", "EnvValue": "127.0.0.1", "EnvDescription": "address of postgres service", "Example": "postgresql-postgresql.devtroncd", "Deprecated": "false"}, {"Env": "PG_DATABASE", "EnvType": "string", "EnvValue": "orchestrator", "EnvDescription": "postgres database to be made connection with", "Example": "orchestrator, casbin, git_sensor, lens", "Deprecated": "false"}, {"Env": "PG_PASSWORD", "EnvType": "string", "EnvValue": "{password}", "EnvDescription": "password for postgres, associated with PG_USER", "Example": "confidential ;)", "Deprecated": "false"}, {"Env": "PG_PORT", "EnvType": "string", "EnvValue": "5432", "EnvDescription": "port of postgresql service", "Example": "5432", "Deprecated": "false"}, {"Env": "PG_READ_TIMEOUT", "EnvType": "int64", "EnvValue": "30", "EnvDescription": "Time out for read operation in postgres", "Example": "", "Deprecated": "false"}, {"Env": "PG_USER", "EnvType": "string", "EnvValue": "postgres", "EnvDescription": "user for postgres", "Example": "postgres", "Deprecated": "false"}, {"Env": "PG_WRITE_TIMEOUT", "EnvType": "int64", "EnvValue": "30", "EnvDescription": "Time out for write operation in postgres", "Example": "", "Deprecated": "false"}]}, {"Category": "RBAC", "Fields": [{"Env": "ENFORCER_CACHE", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "To Enable enforcer cache.", "Example": "", "Deprecated": "false"}, {"Env": "ENFORCER_CACHE_EXPIRATION_IN_SEC", "EnvType": "int", "EnvValue": "86400", "EnvDescription": "Expiration time (in seconds) for enforcer cache. ", "Example": "", "Deprecated": "false"}, {"Env": "ENFORCER_MAX_BATCH_SIZE", "EnvType": "int", "EnvValue": "1", "EnvDescription": "Maximum batch size for the enforcer.", "Example": "", "Deprecated": "false"}, {"Env": "USE_CASBIN_V2", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "To enable casbin V2 API", "Example": "", "Deprecated": "false"}]}]