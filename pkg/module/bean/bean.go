/*
 * Copyright (c) 2020-2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package bean

import (
	"fmt"
	"github.com/caarlos0/env"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

type ModuleInfoDto struct {
	Name                  string                     `json:"name,notnull"`
	Status                string                     `json:"status,notnull" validate:"oneof=notInstalled installed installing installFailed timeout"`
	ModuleResourcesStatus []*ModuleResourceStatusDto `json:"moduleResourcesStatus"`
	Enabled               bool                       `json:"enabled"`
	Moduletype            string                     `json:"moduleType,omitempty"`
}

type ModuleConfigDto struct {
	Enabled bool `json:"enabled"`
}

type BlobStorageConfig struct {
	Enabled bool `env:"BLOB_STORAGE_ENABLED" envDefault:"false"`
}

type ModuleActionRequestDto struct {
	Action     string `json:"action,notnull" validate:"oneof=install"`
	Version    string `json:"version,notnull"`
	ModuleType string `json:"moduleType"`
}
type ModuleEnableRequestDto struct {
	Version string `json:"version,notnull"`
}

type ActionResponse struct {
	Success bool `json:"success"`
}

type ModuleEnvConfig struct {
	ModuleStatusHandlingCronDurationInMin int      `env:"MODULE_STATUS_HANDLING_CRON_DURATION_MIN" envDefault:"3"` // default 3 minutes
	InstalledModules                      []string `env:"INSTALLED_MODULES" envDefault:"" envSeparator:"," description:"List of installed modules given in helm values/yaml are written in cm and used by devtron to know which modules are given" example:"security.trivy,security.clair"`
}

type ModuleResourceStatusDto struct {
	Group         string `json:"group"`
	Version       string `json:"version"`
	Kind          string `json:"kind"`
	Name          string `json:"name"`
	HealthStatus  string `json:"healthStatus"`
	HealthMessage string `json:"healthMessage"`
}

func ParseModuleEnvConfig() (*ModuleEnvConfig, error) {
	cfg := &ModuleEnvConfig{}
	err := env.Parse(cfg)
	if err != nil {
		fmt.Println("failed to parse module env config: " + err.Error())
		return nil, err
	}

	return cfg, nil
}

type ModuleStatus = string
type ModuleName = string

const BlobStorage = "blob-storage"
const (
	CLAIR_V4 = "V4"
	CLAIR_V2 = "V2"
	TRIVY_V1 = "V1"
)

const (
	ModuleStatusNotInstalled  ModuleStatus = "notInstalled"
	ModuleStatusInstalled     ModuleStatus = "installed"
	ModuleStatusInstalling    ModuleStatus = "installing"
	ModuleStatusInstallFailed ModuleStatus = "installFailed"
	ModuleStatusTimeout       ModuleStatus = "timeout"
)

const (
	ModuleNameCiCd              ModuleName = "cicd"
	ModuleNameArgoCd            ModuleName = "argo-cd"
	ModuleNameSecurityClair     ModuleName = "security.clair"
	ModuleNameNotification      ModuleName = "notifier"
	ModuleNameMonitoringGrafana ModuleName = "monitoring.grafana"
	ModuleNameSecurityTrivy     ModuleName = "security.trivy"
)

const (
	MODULE_TYPE_SECURITY string = "security"
)

var SupportedModuleNamesListFirstReleaseExcludingCicd = []string{ModuleNameArgoCd, ModuleNameSecurityClair, ModuleNameNotification, ModuleNameMonitoringGrafana}

type ResourceFilter struct {
	GlobalFilter    *ResourceIdentifier `json:"globalFilter,omitempty"`
	GvkLevelFilters []*GvkLevelFilter   `json:"gvkLevelFilters,omitempty"`
}

type GvkLevelFilter struct {
	Gvk                *schema.GroupVersionKind `json:"gvk"`
	ResourceIdentifier *ResourceIdentifier      `json:"filter"`
}

type ResourceIdentifier struct {
	Labels map[string]string `json:"labels"`
}
