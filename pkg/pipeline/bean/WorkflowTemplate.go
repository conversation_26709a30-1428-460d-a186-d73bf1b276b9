/*
 * Copyright (c) 2024. Devtron Inc.
 */

package bean

import (
	blob_storage "github.com/devtron-labs/common-lib/blob-storage"
	informerBean "github.com/devtron-labs/common-lib/informer"
	"github.com/devtron-labs/devtron/api/bean"
	cacheBean "github.com/devtron-labs/devtron/pkg/pipeline/cacheResourceSelector/bean"
	v1 "k8s.io/api/core/v1"
	v12 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/rest"
	"strconv"
)

type WorkflowTemplate struct {
	WorkflowId       int
	AppName          string
	PipelineName     string
	WorkflowRunnerId int
	Labels           map[string]string
	v1.PodSpec
	ConfigMaps             []bean.ConfigSecretMap
	Secrets                []bean.ConfigSecretMap
	TTLValue               *int32
	WorkflowRequestJson    string
	WorkflowNamePrefix     string
	WfControllerInstanceID string
	ClusterConfig          *rest.Config
	Namespace              string
	ArchiveLogs            bool
	BlobStorageConfigured  bool
	BlobStorageS3Config    *blob_storage.BlobStorageS3Config
	CloudProvider          blob_storage.BlobStorageType
	AzureBlobConfig        *blob_storage.AzureBlobConfig
	GcpBlobConfig          *blob_storage.GcpBlobConfig
	CloudStorageKey        string
	PrePostDeploySteps     []*StepObject
	RefPlugins             []*RefPluginObject
	TerminationGracePeriod int
	WorkflowType           string
	CiCacheResourceName    string
	DevtronInstanceUID     string
}

const (
	CI_WORKFLOW_NAME           = "ci"
	CI_WORKFLOW_WITH_STAGES    = "ci-stages-with-env"
	CiStage                    = "CI"
	JobStage                   = "JOB"
	CdStage                    = "CD"
	CD_WORKFLOW_NAME           = "cd"
	CD_WORKFLOW_WITH_STAGES    = "cd-stages-with-env"
	HELM_JOB_REF_TEMPLATE_NAME = "helm-job-template"
	JOB_CHART_API_VERSION      = "v2"
	JOB_CHART_NAME             = "helm-job"
	JOB_CHART_VERSION          = "0.1.0"
	WorkflowGenerateNamePrefix = "devtron.ai/generate-name-prefix"

	APP_NAME_LABEL_KEY          = "devtron.ai/app-name"
	PIPELINE_NAME_LABEL_KEY     = "devtron.ai/pipeline-name"
	DevtronLabelPurposeKey      = "devtron.ai/purpose"
	DevtronLabelPurposeWorkflow = "workflow"
)

func (workflowTemplate *WorkflowTemplate) GetCiResourceCacheLabels() map[string]string {
	return map[string]string{
		cacheBean.BuildPVCLabelKey1: cacheBean.BuildPVCLabelValue1,
		cacheBean.BuildPVCLabelKey2: workflowTemplate.CiCacheResourceName,
		cacheBean.BuildWorkflowId:   strconv.Itoa(workflowTemplate.WorkflowId),
	}
}

func (workflowTemplate *WorkflowTemplate) GetDevtronCustomLabels() map[string]string {
	devtronCustomLabels := make(map[string]string)
	if len(workflowTemplate.AppName) != 0 {
		devtronCustomLabels[APP_NAME_LABEL_KEY] = workflowTemplate.AppName
	}
	if len(workflowTemplate.PipelineName) != 0 {
		devtronCustomLabels[PIPELINE_NAME_LABEL_KEY] = workflowTemplate.PipelineName
	}
	return devtronCustomLabels
}

func (workflowTemplate *WorkflowTemplate) GetEntrypoint() string {
	switch workflowTemplate.WorkflowType {
	case CI_WORKFLOW_NAME:
		return CI_WORKFLOW_WITH_STAGES
	case CD_WORKFLOW_NAME:
		return CD_WORKFLOW_WITH_STAGES
	default:
		return ""
	}
}

func (workflowTemplate *WorkflowTemplate) CreateObjectMetadata() *v12.ObjectMeta {
	workflowLabels := map[string]string{
		WorkflowGenerateNamePrefix:                workflowTemplate.WorkflowNamePrefix,
		informerBean.WorkflowTypeLabelKey:         workflowTemplate.WorkflowType,
		informerBean.DevtronOwnerInstanceLabelKey: workflowTemplate.DevtronInstanceUID,
	}
	switch workflowTemplate.WorkflowType {
	case CI_WORKFLOW_NAME:
		workflowLabels["devtron.ai/workflow-purpose"] = "ci"
		return &v12.ObjectMeta{
			GenerateName: workflowTemplate.WorkflowNamePrefix + "-",
			Labels:       workflowLabels,
		}
	case CD_WORKFLOW_NAME:
		workflowLabels["devtron.ai/workflow-purpose"] = "cd"
		return &v12.ObjectMeta{
			GenerateName: workflowTemplate.WorkflowNamePrefix + "-",
			Annotations:  map[string]string{"workflows.argoproj.io/controller-instanceid": workflowTemplate.WfControllerInstanceID},
			Labels:       workflowLabels,
		}
	default:
		return nil
	}
}

func (workflowTemplate *WorkflowTemplate) SetActiveDeadlineSeconds(timeout int64) {
	workflowTemplate.ActiveDeadlineSeconds = &timeout
}

type JobManifestTemplate struct {
	NameSpace               string                 `json:"Namespace"`
	Container               v1.Container           `json:"Container"`
	ConfigMaps              []bean.ConfigSecretMap `json:"ConfigMaps"`
	ConfigSecrets           []bean.ConfigSecretMap `json:"ConfigSecrets"`
	Volumes                 []v1.Volume            `json:"Volumes"`
	Toleration              []v1.Toleration        `json:"Toleration"`
	Affinity                v1.Affinity            `json:"Affinity"`
	NodeSelector            map[string]string      `json:"NodeSelector"`
	ActiveDeadlineSeconds   *int64                 `json:"ActiveDeadlineSeconds"`
	TTLSecondsAfterFinished *int32                 `json:"TTLSecondsAfterFinished"`
}
