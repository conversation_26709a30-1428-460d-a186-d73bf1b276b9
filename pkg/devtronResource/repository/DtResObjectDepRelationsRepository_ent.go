package repository

import (
	"github.com/devtron-labs/devtron/pkg/devtronResource/bean"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/go-pg/pg"
	"github.com/go-pg/pg/orm"
	"go.uber.org/zap"
)

type DtResObjDepRelationsRepository interface {
	SaveInBatch(tx *pg.Tx, models []*DevtronResourceObjectDependencyRelations) error
	DeleteAllOfATypeForComponents(tx *pg.Tx, componentResObjIds, componentResSchemaIds []int) error
	DeleteAllForComponentIdAndComponentSchemaId(tx *pg.Tx, componentId, componentSchemaId int) error
	DeleteAllForComponentIdAndComponentSchemaIdAndDepSchemaId(tx *pg.Tx, componentId, componentSchemaId, depSchemaId int, typeOfDep string) error
	DeleteAllIfComponentOrDependencyNotInSchemaIds(tx *pg.Tx, componentOrDependencyId int, schemaId int, notIndrSchemaIds []int) error
	DeleteByDepIdAndTypeForAComponent(tx *pg.Tx, componentId, componentSchemaId int,
		depObjectId int, typeOfDependency string) error
	DeleteByDepIdentifierAndTypeForAComponent(tx *pg.Tx, componentId, componentSchemaId int,
		depObjectIdentifier string, typeOfDependency string) error
	GetAllForAComponent(componentObjectId, componentResObjSchemaId int) ([]*DevtronResourceObjectDependencyRelations, error)
	GetAllForAComponentAndDepType(componentObjectId, componentResObjSchemaId int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error)
	GetAllForAComponentDepAndDepType(componentObjectId, componentResObjSchemaId, depResObjSchemaId int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error)
	GetAllForAComponentTypeAndDepType(componentResObjSchemaId int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error)
	GetAllForMultipleComponentsAndDepType(componentObjectIds []int, componentResObjSchemaId int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error)
	GetAllForADependencyType(depObjectId, resDepSchemaId int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error)
	GetAllForMultipleObjsOfADependencyType(depObjectIds []int, resDepSchemaId int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error)
	GetAllOfATypeForAComponent(componentResObjSchemaId, resDepSchemaId int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error)
	GetAllOfATypeForAComponentForDepsIds(componentResObjSchemaId, resDepSchemaId int, resDepObjIds []int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error)
	GetIdsOfATypeForAComponentForDepsIds(componentResObjSchemaId, resDepSchemaId int, resDepObjIds []int, typeOfDependency string) ([]int, error)
	GetIdsOfATypeForAComponentForDepsIdentifier(componentResObjSchemaId, resDepSchemaId int, resDepObjIdentifiers []string, typeOfDependency string) ([]int, error)
	GetAllOfATypeForAComponentForComponentsIds(componentResObjSchemaId, resDepSchemaId int, resObjIds []int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error)
	GetReleaseTrackIdAndSchemaIdForReleaseId(releaseId, releaseSchemaId int) (int, int, error)
	sql.TransactionWrapper
}

type DtResObjDepRelationsRepositoryImpl struct {
	logger       *zap.SugaredLogger
	dbConnection *pg.DB
	*sql.TransactionUtilImpl
}

func NewDtResObjDepRelationsRepositoryImpl(logger *zap.SugaredLogger,
	dbConnection *pg.DB) *DtResObjDepRelationsRepositoryImpl {
	return &DtResObjDepRelationsRepositoryImpl{
		logger:              logger,
		dbConnection:        dbConnection,
		TransactionUtilImpl: sql.NewTransactionUtilImpl(dbConnection),
	}
}

type DevtronResourceObjectDependencyRelations struct {
	tableName                  struct{} `sql:"devtron_resource_object_dep_relations" pg:",discard_unknown_columns"`
	Id                         int      `sql:"id,pk"`
	ComponentObjectId          int      `sql:"component_object_id"` //can be devtronResourceId or oldObjectId, resolve on the basis of schemaId if needed
	ComponentObjectIdentifier  string   `sql:"component_object_identifier"`
	ComponentDtResSchemaId     int      `sql:"component_dt_res_schema_id"`
	DependencyObjectId         int      `sql:"dependency_object_id"` //can be devtronResourceId or oldObjectId, resolve on the basis of schemaId if needed
	DependencyObjectIdentifier string   `sql:"dependency_object_identifier"`
	DependencyDtResSchemaId    int      `sql:"dependency_dt_res_schema_id"`
	TypeOfDependency           string   `sql:"type_of_dependency"`
	sql.AuditLog
}

func (impl *DtResObjDepRelationsRepositoryImpl) SaveInBatch(tx *pg.Tx, models []*DevtronResourceObjectDependencyRelations) error {
	if tx == nil {
		return impl.dbConnection.Insert(&models)
	}
	err := tx.Insert(&models)
	return err
}

func (impl *DtResObjDepRelationsRepositoryImpl) DeleteAllOfATypeForComponents(tx *pg.Tx, componentResObjIds, componentResSchemaIds []int) error {
	var model DevtronResourceObjectDependencyRelations
	query := tx.Model(&model)
	for i := range componentResObjIds {
		query.WhereOrGroup(func(query *orm.Query) (*orm.Query, error) {
			query.Where("component_object_id =?", componentResObjIds[i]).
				Where("component_dt_res_schema_id = ?", componentResSchemaIds[i])
			return query, nil
		})
	}
	_, err := query.Delete()
	return err
}

func (impl *DtResObjDepRelationsRepositoryImpl) DeleteAllForComponentIdAndComponentSchemaId(tx *pg.Tx, componentResObjId, componentResSchemaId int) error {
	var model DevtronResourceObjectDependencyRelations
	_, err := tx.Model(&model).
		Where("component_object_id = ?", componentResObjId).
		Where("component_dt_res_schema_id = ?", componentResSchemaId).
		Delete()
	return err
}

func (impl *DtResObjDepRelationsRepositoryImpl) DeleteAllForComponentIdAndComponentSchemaIdAndDepSchemaId(tx *pg.Tx, componentResObjId, componentResSchemaId, depSchemaId int, typeOfDep string) error {
	var model DevtronResourceObjectDependencyRelations
	_, err := tx.Model(&model).
		Where("component_object_id = ?", componentResObjId).
		Where("component_dt_res_schema_id = ?", componentResSchemaId).
		Where("dependency_dt_res_schema_id = ?", depSchemaId).
		Where("type_of_dependency = ?", typeOfDep).
		Delete()
	return err
}

func (impl *DtResObjDepRelationsRepositoryImpl) DeleteAllIfComponentOrDependencyNotInSchemaIds(tx *pg.Tx, componentOrDependencyId int, schemaId int, notIndrSchemaIds []int) error {
	var model DevtronResourceObjectDependencyRelations
	query := tx.Model(&model)
	query.WhereOrGroup(func(query *orm.Query) (*orm.Query, error) {
		query.Where("component_object_id =?", componentOrDependencyId).
			Where("component_dt_res_schema_id = ?", schemaId)
		if len(notIndrSchemaIds) > 0 {
			query.Where("dependency_dt_res_schema_id not in (?)", pg.In(notIndrSchemaIds))
		}

		return query, nil
	})
	query.WhereOrGroup(func(query *orm.Query) (*orm.Query, error) {
		query.Where("dependency_object_id =?", componentOrDependencyId).
			Where("dependency_dt_res_schema_id = ?", schemaId)
		if len(notIndrSchemaIds) > 0 {
			query.Where("component_dt_res_schema_id not in (?)", pg.In(notIndrSchemaIds))
		}
		return query, nil
	})

	_, err := query.Delete()
	return err
}

func (impl *DtResObjDepRelationsRepositoryImpl) DeleteByDepIdAndTypeForAComponent(tx *pg.Tx, componentId, componentSchemaId int,
	depObjectId int, typeOfDependency string) error {
	var model DevtronResourceObjectDependencyRelations
	_, err := tx.Model(&model).Where("component_object_id =?", componentId).
		Where("component_dt_res_schema_id = ?", componentSchemaId).
		Where("dependency_object_id = ?", depObjectId).Where("type_of_dependency = ?", typeOfDependency).Delete()
	return err
}

func (impl *DtResObjDepRelationsRepositoryImpl) DeleteByDepIdentifierAndTypeForAComponent(tx *pg.Tx, componentId, componentSchemaId int,
	depObjectIdentifier string, typeOfDependency string) error {
	var model DevtronResourceObjectDependencyRelations
	_, err := tx.Model(&model).Where("component_object_id =?", componentId).
		Where("component_dt_res_schema_id = ?", componentSchemaId).
		Where("dependency_object_identifier = ?", depObjectIdentifier).Where("type_of_dependency = ?", typeOfDependency).Delete()
	return err
}

func (impl *DtResObjDepRelationsRepositoryImpl) GetAllForAComponent(componentObjectId, componentResObjSchemaId int) ([]*DevtronResourceObjectDependencyRelations, error) {
	var models []*DevtronResourceObjectDependencyRelations
	err := impl.dbConnection.Model(&models).Where("component_object_id = ?", componentObjectId).
		Where("component_dt_res_schema_id = ?", componentResObjSchemaId).Select()
	return models, err
}

func (impl *DtResObjDepRelationsRepositoryImpl) GetAllForAComponentAndDepType(componentObjectId, componentResObjSchemaId int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error) {
	var models []*DevtronResourceObjectDependencyRelations
	err := impl.dbConnection.Model(&models).Where("component_object_id = ?", componentObjectId).
		Where("component_dt_res_schema_id = ?", componentResObjSchemaId).
		Where("type_of_dependency = ?", typeOfDependency).Select()
	return models, err
}

func (impl *DtResObjDepRelationsRepositoryImpl) GetAllForAComponentDepAndDepType(componentObjectId, componentResObjSchemaId, depResObjSchemaId int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error) {
	var models []*DevtronResourceObjectDependencyRelations
	err := impl.dbConnection.Model(&models).Where("component_object_id = ?", componentObjectId).
		Where("component_dt_res_schema_id = ?", componentResObjSchemaId).
		Where("dependency_dt_res_schema_id = ?", depResObjSchemaId).
		Where("type_of_dependency = ?", typeOfDependency).Select()
	return models, err
}

func (impl *DtResObjDepRelationsRepositoryImpl) GetAllForAComponentTypeAndDepType(componentResObjSchemaId int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error) {
	var models []*DevtronResourceObjectDependencyRelations
	err := impl.dbConnection.Model(&models).
		Where("component_dt_res_schema_id = ?", componentResObjSchemaId).
		Where("type_of_dependency = ?", typeOfDependency).Select()
	return models, err
}

func (impl *DtResObjDepRelationsRepositoryImpl) GetAllForMultipleComponentsAndDepType(componentObjectIds []int, componentResObjSchemaId int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error) {
	var models []*DevtronResourceObjectDependencyRelations
	err := impl.dbConnection.Model(&models).Where("component_object_id in (?)", pg.In(componentObjectIds)).
		Where("component_dt_res_schema_id = ?", componentResObjSchemaId).
		Where("type_of_dependency = ?", typeOfDependency).Select()
	return models, err
}

func (impl *DtResObjDepRelationsRepositoryImpl) GetAllForADependencyType(depObjectId, resDepSchemaId int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error) {
	var models []*DevtronResourceObjectDependencyRelations
	err := impl.dbConnection.Model(&models).Where("dependency_object_id = ?", depObjectId).
		Where("dependency_dt_res_schema_id = ?", resDepSchemaId).
		Where("type_of_dependency = ?", typeOfDependency).Select()
	return models, err
}

func (impl *DtResObjDepRelationsRepositoryImpl) GetAllForMultipleObjsOfADependencyType(depObjectIds []int, resDepSchemaId int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error) {
	var models []*DevtronResourceObjectDependencyRelations
	err := impl.dbConnection.Model(&models).Where("dependency_object_id in (?)", pg.In(depObjectIds)).
		Where("dependency_dt_res_schema_id = ?", resDepSchemaId).
		Where("type_of_dependency = ?", typeOfDependency).Select()
	return models, err
}

func (impl *DtResObjDepRelationsRepositoryImpl) GetAllOfATypeForAComponent(componentResObjSchemaId, resDepSchemaId int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error) {
	var models []*DevtronResourceObjectDependencyRelations
	err := impl.dbConnection.Model(&models).Where("component_dt_res_schema_id = ?", componentResObjSchemaId).
		Where("dependency_dt_res_schema_id = ?", resDepSchemaId).
		Where("type_of_dependency = ?", typeOfDependency).Select()
	return models, err
}

func (impl *DtResObjDepRelationsRepositoryImpl) GetAllOfATypeForAComponentForDepsIds(componentResObjSchemaId, resDepSchemaId int, resDepObjIds []int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error) {
	var models []*DevtronResourceObjectDependencyRelations
	err := impl.dbConnection.Model(&models).Where("component_dt_res_schema_id = ?", componentResObjSchemaId).
		Where("dependency_dt_res_schema_id = ?", resDepSchemaId).
		Where("dependency_object_id in (?)", pg.In(resDepObjIds)).
		Where("type_of_dependency = ?", typeOfDependency).Select()
	return models, err
}

func (impl *DtResObjDepRelationsRepositoryImpl) GetIdsOfATypeForAComponentForDepsIds(componentResObjSchemaId, resDepSchemaId int, resDepObjIds []int, typeOfDependency string) ([]int, error) {
	var componentIds []int
	err := impl.dbConnection.Model().
		Table("devtron_resource_object_dep_relations").
		Column("devtron_resource_object_dep_relations.component_object_id").
		Where("component_dt_res_schema_id = ?", componentResObjSchemaId).
		Where("dependency_dt_res_schema_id = ?", resDepSchemaId).
		Where("dependency_object_id in (?)", pg.In(resDepObjIds)).
		Where("type_of_dependency = ?", typeOfDependency).
		Select(&componentIds)
	return componentIds, err
}

func (impl *DtResObjDepRelationsRepositoryImpl) GetIdsOfATypeForAComponentForDepsIdentifier(componentResObjSchemaId, resDepSchemaId int, resDepObjIdentifiers []string, typeOfDependency string) ([]int, error) {
	var componentIds []int
	err := impl.dbConnection.Model().
		Table("devtron_resource_object_dep_relations").
		Column("devtron_resource_object_dep_relations.component_object_id").
		Where("component_dt_res_schema_id = ?", componentResObjSchemaId).
		Where("dependency_dt_res_schema_id = ?", resDepSchemaId).
		Where("dependency_object_identifier in (?)", pg.In(resDepObjIdentifiers)).
		Where("type_of_dependency = ?", typeOfDependency).
		Select(&componentIds)
	return componentIds, err
}

func (impl *DtResObjDepRelationsRepositoryImpl) GetAllOfATypeForAComponentForComponentsIds(componentResObjSchemaId, resDepSchemaId int, resObjIds []int, typeOfDependency string) ([]*DevtronResourceObjectDependencyRelations, error) {
	if len(resObjIds) == 0 {
		return nil, nil
	}
	var models []*DevtronResourceObjectDependencyRelations
	err := impl.dbConnection.Model(&models).Where("component_dt_res_schema_id = ?", componentResObjSchemaId).
		Where("dependency_dt_res_schema_id = ?", resDepSchemaId).
		Where("component_object_id in (?)", pg.In(resObjIds)).
		Where("type_of_dependency = ?", typeOfDependency).Select()
	return models, err
}

func (impl *DtResObjDepRelationsRepositoryImpl) GetReleaseTrackIdAndSchemaIdForReleaseId(releaseId, releaseSchemaId int) (int, int, error) {
	var models DevtronResourceObjectDependencyRelations
	err := impl.dbConnection.Model(&models).Where("component_object_id = ?", releaseId).
		Where("component_dt_res_schema_id = ?", releaseSchemaId).
		Where("type_of_dependency = ?", bean.DevtronResourceDependencyTypeParent).Select()
	if err != nil {
		return 0, 0, err
	}
	return models.DependencyObjectId, models.DependencyDtResSchemaId, err
}
