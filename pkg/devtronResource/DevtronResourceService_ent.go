/*
 * Copyright (c) 2024. Devtron Inc.
 */

package devtronResource

import (
	"context"
	"encoding/json"
	"fmt"
	apiBean "github.com/devtron-labs/devtron/api/devtronResource/bean"
	repository3 "github.com/devtron-labs/devtron/internal/sql/repository"
	helper2 "github.com/devtron-labs/devtron/internal/sql/repository/helper"
	"github.com/devtron-labs/devtron/pkg/apiToken"
	"github.com/devtron-labs/devtron/pkg/app"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	bean2 "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"github.com/devtron-labs/devtron/pkg/bean/common/pagination"
	"github.com/devtron-labs/devtron/pkg/cluster/environment"
	clusterRepository "github.com/devtron-labs/devtron/pkg/cluster/repository"
	"github.com/devtron-labs/devtron/pkg/deployment/common"
	"github.com/devtron-labs/devtron/pkg/devtronResource/adapter"
	"github.com/devtron-labs/devtron/pkg/devtronResource/audit"
	"github.com/devtron-labs/devtron/pkg/devtronResource/helper"
	"github.com/devtron-labs/devtron/pkg/devtronResource/in"
	"github.com/devtron-labs/devtron/pkg/devtronResource/read"
	util2 "github.com/devtron-labs/devtron/pkg/devtronResource/util"
	"github.com/devtron-labs/devtron/pkg/pipeline"
	"github.com/devtron-labs/devtron/pkg/sql"
	read2 "github.com/devtron-labs/devtron/pkg/team/read"
	"go.opentelemetry.io/otel"
	slices2 "golang.org/x/exp/slices"
	"math"
	"net/http"
	"time"

	appRepository "github.com/devtron-labs/devtron/internal/sql/repository/app"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/util"
	repository2 "github.com/devtron-labs/devtron/pkg/auth/user/repository"
	"github.com/devtron-labs/devtron/pkg/devtronResource/bean"
	"github.com/devtron-labs/devtron/pkg/devtronResource/repository"
	"github.com/go-pg/pg"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"go.uber.org/zap"
)

// Some common abbreviation that are being used in devtron resource package
//Dt -> Devtron
//Res -> Resource
//Obj -> object
//Dep(s) -> Dependency(ies)
//Req -> Request
//Resp -> Response
//Int -> Internal
//Desc -> Descriptor

type DevtronResourceService interface {
	// GetDtResList is used to list out all devtron resources and its schema details in []bean.DtResBean
	GetDtResList(onlyIsExposed bool) ([]*bean.DtResBean, error)
	// ListResObjByKindAndVersion will list out all the resource objects by kind, subKind and version
	//
	// Query Flag:
	//
	// 1. isLite
	//    - true for lightweight data // provides the bean.DtResObjDescApiBean only
	//    - false for detailed data   // provides the complete bean.DtResObjBasicDataBean
	// 2. fetchChild
	//    - true to resource data along with children data 	// includes the ChildObjects also
	//    - false for resource data only   					// doesn't include the ChildObjects
	// 3. filterCondition
	//    - supports filtering on resource object list b=in format resource|type|comma seperated values
	//    - release-track|id|12,13,14
	ListResObjByKindAndVersion(kind, subKind, version string, isLite, fetchChild bool, filterCriteria []string,
		authorisedMapObjDto apiBean.GetObjListAuthorisedDtoMap) (pagination.PaginatedResponse[*bean.DtResObjGetAPIBean], error)
	// GetResObj will get the bean.DtResObjGetAPIBean based on the given bean.DtResObjDescApiBean
	GetResObj(req *bean.DtResObjDescApiBean, queryParams apiBean.GetResourceQueryParams, userId int32) (*bean.DtResObjGetAPIBean, error)
	// CreateResObj creates resource object corresponding to kind,version according to bean.DtResObjCreateReqBean
	CreateResObj(ctx context.Context, reqBean *bean.DtResObjCreateReqBean, userId int32) error
	// CreateOrUpdateResObj is only used for catalog
	CreateOrUpdateResObj(ctx context.Context, reqBean *bean.DtResObjCatalogReqBean, userId int32) error
	// PatchResObj supports json patch operation corresponding to kind,subKind,version on json object data takes in ([]PatchQuery in DevtronResourceObjectBean), returns error if any
	PatchResObj(ctx context.Context, req *bean.DtResObjPatchReqBean, userId int32) (*bean.SuccessResponse, error)
	// DeleteResObj deletes resource object corresponding to kind,version, id or name
	DeleteResObj(ctx context.Context, req *bean.DtResObjDescApiBean, queryParams apiBean.GetResourceQueryParams, userId int32) (*bean.SuccessResponse, error)

	// CloneResObj clones resource object within the same kind and version and within the same parent resource object
	CloneResObj(ctx context.Context, req *bean.DtResObjCloneReqBean, userId int32) error

	// GetResDeps will get the bean.DtResObjDepsReqBean based on the given bean.DtResObjDescApiBean
	// It provides the dependencies and child dependencies []bean.DtResDepBean
	GetResDeps(req *bean.DtResObjDescApiBean, query *apiBean.GetDependencyQueryParams, userId int32) (*bean.DtResObjDepsReqBean, error)

	GetDepOptions(ctx context.Context, req *bean.DtResObjDescApiBean, query *apiBean.GetDependencyOptionsQueryParams) ([]*bean.DepOptions, error)

	// GetDepConfigOptions is used to populate options for selecting config data for any resource dependency.
	// It operates on bean.GetConfigOptionsQueryParams and return a generic response of type []bean.DepConfigOptions[T any]
	GetDepConfigOptions(req *bean.DtResObjDescApiBean, query *apiBean.GetConfigOptionsQueryParams, userId int32) (any, error)
	// GetDepsOffendingState is used to get offending state for a devtron resource deps, here offending signifies current status of the deps.
	GetDepsOffendingState(req *bean.DtResObjDescApiBean, query *apiBean.GetDepsOffendingQueryParams, userId int32) (*bean.DepOffendingGetApiBean, error)
	// CreateOrUpdateResDeps is used to PUT dependencies data for resource (currently supports resource catalogue)
	CreateOrUpdateResDeps(ctx context.Context, req *bean.DtResObjDepsReqBean, userId int32) error
	// PatchResDeps supports json patch operation corresponding to kind,subKind,version on json object data takes in ([]PatchQuery in DevtronResourceObjectBean), returns error if any
	PatchResDeps(ctx context.Context, req *bean.DtResDepPatchAPIBean, userId int32) (*bean.SuccessResponse, error)
	// FindNumberOfApplicationsWithDependenciesMapped is used to provide the number of
	// devtron resource objects those have dependencies in there resource objects.
	FindNumberOfApplicationsWithDependenciesMapped() (int, error)

	GetResSummary(ctx context.Context, req *bean.DtResObjDescApiBean) (*bean.DtResObjSummaryResp, error)

	TaskRunService
}

type TaskRunService interface {
	// GetTaskRunInfoWithFilters is used to give the task run (deployment) detailed status for a release object.
	//
	// Query Flag: bean.GetTaskRunInfoQueryParams
	//
	// 1. IsLite
	// 		- true : fetch level data with allowedDeployment flag, excluding application's release status
	// 		- false : fetch level data without allowedDeployment flag and includes application's release status
	// 2. LevelIndex
	// 		- Equal To 0 : fetch all level data.
	// 		- Greater Than 1 : fetch the specified level data.
	GetTaskRunInfoWithFilters(req *bean.TaskInfoPostApiBean, query *apiBean.GetTaskRunInfoQueryParams, userId int32) (*bean.DeploymentTaskInfoResponse, error)
	// ExecuteTask method executes a task for the devtron resource and performs dry run if set to true in request , starts.
	ExecuteTask(ctx context.Context, req *bean.DevResTaskExecutionBean, authorisedTaskTargetIds map[string]bool, processednfo *TaskExecutionProcessInfo, existingObject *repository.DevtronResourceObject, userMetadata *bean2.UserMetadata) ([]*bean.TaskExecutionResponseBean, error)
	GetProcessInfoForRbacAndTaskExecution(req *bean.DevResTaskExecutionBean, userId int32) (*TaskExecutionProcessInfo, *repository.DevtronResourceObject, error)
}

type DevtronResourceServiceImpl struct {
	logger                          *zap.SugaredLogger
	dtResRepository                 repository.DevtronResourceRepository
	dtResSchemaRepository           repository.DevtronResourceSchemaRepository
	dtResObjectRepository           repository.DevtronResourceObjectRepository
	dtResTaskRunRepository          repository.DevtronResourceTaskRunRepository
	dtResObjAuditRepository         repository.DevtronResourceObjectAuditRepository
	dtResObjDepsRelationsRepository repository.DtResObjDepRelationsRepository
	appRepository                   appRepository.AppRepository //TODO: remove repo dependency
	pipelineRepository              pipelineConfig.PipelineRepository
	userRepository                  repository2.UserRepository
	clusterRepository               clusterRepository.ClusterRepository
	dtResInternalProcessingService  in.InternalProcessingService
	dtResReadService                read.ReadService
	dtResObjectAuditService         audit.ObjectAuditService
	envService                      environment.EnvironmentService
	dtResObjRelationReadService     read.DtResObjRelationReadService
	deploymentConfigService         common.DeploymentConfigService
	appCrudService                  app.AppCrudOperationService
	userService                     user.UserService
	apiTokenService                 apiToken.ApiTokenService
	dockerRegistryConfig            pipeline.DockerRegistryConfig
	teamReadService                 read2.TeamReadService
}

func NewDevtronResourceServiceImpl(logger *zap.SugaredLogger, devtronResourceRepository repository.DevtronResourceRepository,
	devtronResourceSchemaRepository repository.DevtronResourceSchemaRepository, devtronResourceObjectRepository repository.DevtronResourceObjectRepository,
	dtResourceTaskRunRepository repository.DevtronResourceTaskRunRepository, devtronResourceObjectAuditRepository repository.DevtronResourceObjectAuditRepository,
	dtResObjDepsRelationsRepository repository.DtResObjDepRelationsRepository,
	appRepository appRepository.AppRepository, pipelineRepository pipelineConfig.PipelineRepository,
	userRepository repository2.UserRepository, clusterRepository clusterRepository.ClusterRepository,
	dtResourceInternalProcessingService in.InternalProcessingService, dtResourceReadService read.ReadService,
	dtResourceObjectAuditService audit.ObjectAuditService, envService environment.EnvironmentService,
	dtResObjRelationReadService read.DtResObjRelationReadService, deploymentConfigService common.DeploymentConfigService,
	appCrudService app.AppCrudOperationService, userService user.UserService,
	apiTokenService apiToken.ApiTokenService, dockerRegistryConfig pipeline.DockerRegistryConfig,
	teamReadService read2.TeamReadService) *DevtronResourceServiceImpl {
	impl := &DevtronResourceServiceImpl{
		logger:                          logger,
		dtResRepository:                 devtronResourceRepository,
		dtResSchemaRepository:           devtronResourceSchemaRepository,
		dtResObjectRepository:           devtronResourceObjectRepository,
		dtResTaskRunRepository:          dtResourceTaskRunRepository,
		dtResObjAuditRepository:         devtronResourceObjectAuditRepository,
		dtResObjDepsRelationsRepository: dtResObjDepsRelationsRepository,
		appRepository:                   appRepository,
		pipelineRepository:              pipelineRepository,
		userRepository:                  userRepository,
		clusterRepository:               clusterRepository,
		dtResInternalProcessingService:  dtResourceInternalProcessingService,
		dtResReadService:                dtResourceReadService,
		dtResObjectAuditService:         dtResourceObjectAuditService,
		envService:                      envService,
		dtResObjRelationReadService:     dtResObjRelationReadService,
		deploymentConfigService:         deploymentConfigService,
		appCrudService:                  appCrudService,
		userService:                     userService,
		apiTokenService:                 apiTokenService,
		dockerRegistryConfig:            dockerRegistryConfig,
		teamReadService:                 teamReadService,
	}

	return impl
}

//
//Get resource list and related method starts

func (impl *DevtronResourceServiceImpl) GetDtResList(onlyIsExposed bool) ([]*bean.DtResBean, error) {
	//getting all resource details from cache only as resource crud is not available as of now
	devtronResourceSchemas := impl.dtResReadService.GetDtResourcesSchemaByIdMap()
	devtronResources := impl.dtResReadService.GetDtResourcesByIdMap()
	response := make([]*bean.DtResBean, 0, len(devtronResources))
	resourceIdAndObjectIndexMap := make(map[int]int, len(devtronResources))
	i := 0
	for _, devtronResource := range devtronResources {
		if onlyIsExposed && !devtronResource.IsExposed {
			continue
		}
		response = append(response, &bean.DtResBean{
			DevtronResourceId: devtronResource.Id,
			Kind:              devtronResource.Kind,
			DisplayName:       devtronResource.DisplayName,
			Description:       devtronResource.Description,
			LastUpdatedOn:     devtronResource.UpdatedOn,
		})
		resourceIdAndObjectIndexMap[devtronResource.Id] = i
		i++
	}
	for _, devtronResourceSchema := range devtronResourceSchemas {
		//getting index where resource of this schema is present
		index := resourceIdAndObjectIndexMap[devtronResourceSchema.DevtronResourceId]
		response[index].VersionSchemaDetails = append(response[index].VersionSchemaDetails, &bean.DtResSchemaBean{
			DevtronResourceSchemaId: devtronResourceSchema.Id,
			Version:                 devtronResourceSchema.Version,
		})
	}
	return response, nil
}

//Get resource list and related method ends
//

//
//list resource object and related method starts

func (impl *DevtronResourceServiceImpl) ListResObjByKindAndVersion(kind, subKind, version string, isLite, fetchChild bool,
	filterCriteria []string, authorisedMapObjDto apiBean.GetObjListAuthorisedDtoMap) (pagination.PaginatedResponse[*bean.DtResObjGetAPIBean], error) {
	response := pagination.NewPaginatedResponse[*bean.DtResObjGetAPIBean]()
	_, resourceSchema, err := impl.dtResReadService.GetResAndSchemaFromResType(adapter.BuildDtResTypeInternalReq(kind, subKind, version))
	if err != nil {
		impl.logger.Errorw("error in ListResObjByKindAndVersion", "kind", kind, "version", version, "err", err)
		return response, err
	}
	resourceObjects, err := impl.dtResObjectRepository.GetAllWithSchemaId(resourceSchema.Id)
	if err != nil && !util.IsErrNoRows(err) {
		impl.logger.Errorw("error in getting objects by resourceSchemaId", "err", err, "resourceSchemaId", resourceSchema.Id)
		return response, err
	}
	filterCategoriseReq, err := impl.categoriseFilterCriteria(kind, subKind, version, filterCriteria)
	if err != nil {
		impl.logger.Errorw("error encountered in ListResObjByKindAndVersion", "err", err)
		return response, err
	}
	if len(filterCategoriseReq.DtResObjFilter) > 0 {
		//applying filters and getting filtered resource objects
		resourceObjects, err = impl.applyFilterCriteriaForResourceObjectsList(kind, subKind, version, resourceObjects, filterCategoriseReq.DtResObjFilter)
		if err != nil {
			impl.logger.Errorw("error in decodeFilterCriteria", "filterCriteria", filterCriteria, "err", err)
			return response, err
		}
	}
	// filters application ends resource objects are filtered.
	resObjIdChildObjsMap := make(map[int][]*repository.DevtronResourceObject)
	if fetchChild {
		resObjIdChildObjsMap, err = impl.fetchChildObjectsAndIndexMapForMultipleObjects(resourceObjects, resourceSchema.Id, kind)
		if err != nil {
			impl.logger.Errorw("error, fetchChildObjectsAndIndexMapForMultipleObjects", "err", err, "kind", kind, "subKind", subKind, "version", version)
			return response, err
		}
	}
	response.UpdateTotalCount(len(resourceObjects))
	response.UpdateOffset(0)
	response.UpdateSize(len(resourceObjects))
	f1 := getFuncToListApiResKind(kind)
	if f1 == nil {
		impl.logger.Errorw("error kind type not supported", "err", err, "kind", kind)
		return response, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceKindOrComponent, bean.InvalidResourceKindOrComponent)
	}
	response.Data, err = f1(impl, resourceObjects, resObjIdChildObjsMap, isLite, authorisedMapObjDto, filterCategoriseReq)
	if err != nil {
		impl.logger.Errorw("error in getting list response", "err", err, "kind", kind, "subKind", subKind, "version", version)
		return response, err
	}
	return response, nil
}

func (impl *DevtronResourceServiceImpl) categoriseFilterCriteria(kind, subKind, version string, filterCriteria []string) (categoriseFilterReq *bean.FilterCriteriaCategoriseReq, err error) {
	f := getFuncToCategoriseFilterCriteriaForListResourceApi(kind, subKind, version)
	if f == nil {
		categoriseFilterReq, err = util2.GenericCategoriseFilterCriteria(filterCriteria)
		if err != nil {
			impl.logger.Errorw("error encountered in categoriseFilterCriteria", "filterCriteria", filterCriteria, "err", err)
			return nil, err
		}
	} else {
		categoriseFilterReq, err = f(impl, filterCriteria)
		if err != nil {
			impl.logger.Errorw("error encountered in categoriseFilterCriteria", "filterCriteria", filterCriteria, "err", err)
			return nil, err
		}
	}
	return categoriseFilterReq, nil

}

func (impl *DevtronResourceServiceImpl) fetchChildObjectsAndIndexMapForMultipleObjects(resourceObjects []*repository.DevtronResourceObject, schemaId int,
	kind string) (map[int][]*repository.DevtronResourceObject, error) {
	respMap := make(map[int][]*repository.DevtronResourceObject, len(resourceObjects))
	if !(kind == bean.DevtronResourceReleaseTrack.ToString() || kind == bean.DevtronResourceTenant.ToString()) {
		//do nothing as other resources supporting list api does not have child objects
		return respMap, nil
	}
	//getting ids of resourceObjects
	resObjIds := make([]int, 0, len(resourceObjects))
	for _, resObj := range resourceObjects {
		resObjIds = append(resObjIds, resObj.Id)
	}
	allChildMappings, err := impl.dtResObjRelationReadService.GetChildMappingsForMultipleParents(resObjIds, schemaId)
	if err != nil {
		impl.logger.Errorw("error, GetChildMappingsForMultipleParents", "resObjIds", resObjIds, "schemaId", schemaId, "err", err)
		return nil, err
	}
	//we know that this method is only supported for release tracks and tenants and only one type of child is allowed currently for a parent so we will keep only one schemaId
	childObjIds := make([]int, 0, len(allChildMappings))
	childSchemaId := 0
	resObjKeyChildMapping := make(map[int][]int)
	for _, childMapping := range allChildMappings {
		childObjIds = append(childObjIds, childMapping.ComponentObjectId)
		childSchemaId = childMapping.ComponentDtResSchemaId
		resObjKeyChildMapping[childMapping.DependencyObjectId] = append(resObjKeyChildMapping[childMapping.DependencyObjectId], childMapping.ComponentObjectId)
	}
	if len(childObjIds) > 0 {
		childObjects, err := impl.dtResObjectRepository.GetAllObjectByIdsOrOldObjectIds(childObjIds, nil, childSchemaId)
		if err != nil {
			impl.logger.Errorw("error, GetAllObjectByIdsOrOldObjectIds", "err", err, "objectIds", childObjIds, "schemaId", childSchemaId)
			return nil, err
		}
		childObjectIdObjectsMap := make(map[int]int) //map of resObjectId and index of object in array
		for i, childResourceObject := range childObjects {
			childObjectIdObjectsMap[childResourceObject.Id] = i
		}
		for parentId, childIds := range resObjKeyChildMapping {
			for _, childId := range childIds {
				if valIndex, ok := childObjectIdObjectsMap[childId]; ok {
					respMap[parentId] = append(respMap[parentId], childObjects[valIndex])
				}
			}
		}
	}
	return respMap, nil
}

//list resource object and related method starts
//

//
//Get resource object and related method starts

func (impl *DevtronResourceServiceImpl) GetResObj(req *bean.DtResObjDescApiBean, queryParams apiBean.GetResourceQueryParams, userId int32) (*bean.DtResObjGetAPIBean, error) {
	resp := &bean.DtResObjGetAPIBean{
		DtResObjDescApiBean:   &bean.DtResObjDescApiBean{},
		DtResObjBasicDataBean: &bean.DtResObjBasicDataBean{},
	}

	internalDescriptorBean := adapter.BuildIntDescBeanFromApiBean(req, userId)
	adapter.SetIdAndIdentifierInInternalDescriptorBean(internalDescriptorBean, queryParams.Id, queryParams.Identifier)
	resourceSchema, existingResourceObject, err := impl.getResourceSchemaAndExistingObject(internalDescriptorBean)
	if err != nil {
		return nil, err
	}
	resp.Schema = resourceSchema.Schema
	if existingResourceObject == nil || existingResourceObject.Id == 0 {
		if req.Kind == bean.DevtronResourceRelease.ToString() || req.Kind == bean.DevtronResourceReleaseTrack.ToString() || req.Kind == bean.DevtronResourceReleaseChannel.ToString() {
			impl.logger.Warnw("invalid get request, object not found", "req", req)
			return nil, util.GetApiErrorAdapter(http.StatusNotFound, "404", bean.ResourceDoesNotExistMessage, bean.ResourceDoesNotExistMessage)
		}
	}
	resourceObject := &bean.DtResObjGetAPIBean{
		DtResObjDescApiBean:   req,
		DtResObjBasicDataBean: &bean.DtResObjBasicDataBean{},
	}
	if queryParams.Component == nil || len(queryParams.Component) == 0 {
		// if no components are defined, fetch the complete data
		queryParams.Component = []bean.DtResUIComponent{bean.UIComponentAll}
	}
	for _, component := range queryParams.Component {
		f := getFuncForGetApiResKindUIComponent(req.Kind, component.ToString()) //getting function for component requested from UI
		if f == nil {
			impl.logger.Errorw("error component type not supported", "err", err, "kind", req.Kind, "component", component)
			return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceKindOrComponent, bean.InvalidResourceKindOrComponent)
		}
		err = f(impl, resourceSchema, existingResourceObject, resourceObject, internalDescriptorBean)
		if err != nil {
			impl.logger.Errorw("error, GetResObj", "err", err, "kind", req.Kind, "component", component)
			return nil, err
		}
	}
	// processed and got required fields in internal bean now setting it to api bean for response.
	resourceObject.DtResObjDescApiBean = adapter.BuildDtDescBeanFromInternalBean(internalDescriptorBean)
	return resourceObject, nil
}

// getIdVsIdentifierMapForResIds get map of id VS identifiers for resource object ids, not supported for resources whose resource objects are not created in devtron_resource_object like (devtron-apps, helm-apps, job, cd-pipelines)
func (impl *DevtronResourceServiceImpl) getIdVsIdentifierMapForResIds(resIds []int, schemaId int) (map[int]string, error) {
	idVsIdentifierMap := make(map[int]string)
	if len(resIds) > 0 {
		resObjects, err := impl.dtResObjectRepository.GetWithOnlyIdentifierSetByIds(resIds)
		if err != nil {
			impl.logger.Errorw("error encountered in getIdVsIdentifierMapForIds", "resIds", resIds, "schemaId", schemaId, "err", err)
			return nil, err
		}

		for _, obj := range resObjects {
			idVsIdentifierMap[obj.Id] = obj.Identifier
		}
	}
	return idVsIdentifierMap, nil
}

//Get resource object and related method ends
//

//
//Create resource object and related method starts

func (impl *DevtronResourceServiceImpl) CreateResObj(ctx context.Context, reqBean *bean.DtResObjCreateReqBean, userId int32) error {
	newCtx, span := otel.Tracer("DevtronResourceService").Start(ctx, "CreateResObj")
	defer span.End()
	err := validateCreateResourceRequest(reqBean)
	if err != nil {
		return err
	}
	err = impl.populateDefaultValuesForCreateReq(reqBean, userId)
	if err != nil {
		return err
	}
	internalDescriptorBean := adapter.BuildIntDescBeanFromApiBean(reqBean.DtResObjDescApiBean, userId)
	//getting schema latest from the db (not getting it from FE for edge cases when schema has got updated
	//just before an object update is requested)
	devtronResourceSchema, existingResourceObjectFound, err := impl.getResourceSchemaAndCheckIfObjectFound(internalDescriptorBean)
	if err != nil {
		return err
	}
	if existingResourceObjectFound {
		impl.logger.Errorw("error encountered in CreateResObj", "request", reqBean, "err", err)
		return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.ResourceAlreadyExistsMessage, bean.ResourceAlreadyExistsMessage)
	}

	resourceObjReq := adapter.GetRequirementReqForCreateRequest(reqBean, internalDescriptorBean, "", false)
	_, err = impl.createOrUpdateDevResObj(newCtx, nil, resourceObjReq, devtronResourceSchema, nil, nil)
	return err
}

func (impl *DevtronResourceServiceImpl) getResourceSchemaAndCheckIfObjectFound(reqBean *bean.DtResObjectInternalDescBean) (*repository.DevtronResourceSchema, bool, error) {
	_, devtronResourceSchema, err := impl.dtResReadService.GetResAndSchemaFromResType(adapter.BuildDtResTypeInternalReq(reqBean.Kind, reqBean.SubKind, reqBean.Version))
	if err != nil {
		impl.logger.Errorw("error in getResourceSchemaAndCheckIfObjectFound", "kind", reqBean.Kind, "version", reqBean.Version, "err", err)
		err = util.GetApiErrorAdapter(http.StatusNotFound, "404", bean.InvalidResourceKindOrVersion, bean.InvalidResourceKindOrVersion)
		return nil, false, err
	}
	exists, err := impl.dtResReadService.CheckIfExistingDtObj(reqBean.Id, devtronResourceSchema.Id, reqBean.IdType, reqBean.Identifier)
	if err != nil {
		impl.logger.Errorw("error in getting object by id or name", "err", err, "request", reqBean)
		return devtronResourceSchema, false, err
	}
	return devtronResourceSchema, exists, nil
}

func (impl *DevtronResourceServiceImpl) getIdTypeBasedOnKindOrSchemaId(kind string, schemaId int) (bean.IdType, error) {
	var err error
	if kind == "" && schemaId > 0 {
		kind, _, _, err = helper.GetKindSubKindAndVersionOfResourceBySchemaId(schemaId, impl.dtResReadService.GetDtResourcesSchemaByIdMap(), impl.dtResReadService.GetDtResourcesByIdMap())
		if err != nil {
			impl.logger.Errorw("error encountered in getExistingObjectsMap", "DevtronResourceSchemaId", schemaId, "err", err)
			return bean.OldObjectId, err
		}
	}
	if kind == bean.DevtronResourceRelease.ToString() || kind == bean.DevtronResourceReleaseTrack.ToString() || kind == bean.DevtronResourceTaskRun.ToString() || kind == bean.DevtronResourceReleaseChannel.ToString() {
		// for bean.DevtronResourceReleaseTrack and bean.DevtronResourceRelease
		// there is no OldObjectId, here the Id -> repository.DevtronResourceObject.Id (own id)
		return bean.ResourceObjectIdType, nil
	}
	// by default fall back is old object id , need to change when new entries come
	return bean.OldObjectId, nil
}

// populateDefaultValuesForCreateReq is used to fill the default values of some fields for Create Resource request only.
func (impl *DevtronResourceServiceImpl) populateDefaultValuesForCreateReq(reqBean *bean.DtResObjCreateReqBean, userId int32) error {
	f := getFuncToPopulateDefaultValuesForCreateResReq(reqBean.Kind, reqBean.SubKind, reqBean.Version)
	if f != nil {
		return f(impl, reqBean, userId)
	}
	return nil
}

func validateCreateResourceRequest(reqBean *bean.DtResObjCreateReqBean) error {
	f := getFuncToValidateCreateResReq(reqBean.Kind, reqBean.SubKind, reqBean.Version)
	if f != nil {
		return f(reqBean)
	}
	return nil
}

//Create resource object and related method ends
//

//
//Create/Update(old method used for catalogue) resource object and related method starts

func (impl *DevtronResourceServiceImpl) CreateOrUpdateResObj(ctx context.Context, reqBean *bean.DtResObjCatalogReqBean, userId int32) error {
	newCtx, span := otel.Tracer("DevtronResourceService").Start(ctx, "CreateOrUpdateResObj")
	defer span.End()
	internalDescriptorBean := adapter.BuildIntDescBeanFromApiBean(reqBean.DtResObjDescApiBean, userId)
	//getting schema latest from the db (not getting it from FE for edge cases when schema has got updated
	//just before an object update is requested)
	devtronResourceSchema, devtronResourceObject, err := impl.getResourceSchemaAndExistingObject(internalDescriptorBean)
	if err != nil {
		return err
	}
	if devtronResourceObject != nil && devtronResourceObject.Id > 0 {
		//same flow being used for catalog of non-release entities where this can be going to be created first time, so only checking in case object is already created
		err = impl.checkIfResourcePatchOperationValid(internalDescriptorBean,
			devtronResourceObject.ObjectData, "", []bean.PatchQuery{{Path: bean.CatalogQueryPath}})
		if err != nil {
			impl.logger.Errorw("err, checkIfResourcePatchOperationValid", "err", err, "req", reqBean)
			return err
		}
	}
	resourceObjReq := adapter.GetRequirementRequestForCatalogRequest(reqBean, internalDescriptorBean, false)
	_, err = impl.createOrUpdateDevResObj(newCtx, nil, resourceObjReq, devtronResourceSchema, devtronResourceObject, nil)
	return err
}

// checkIfResourcePatchOperationValid : dummy implementation
func (impl *DevtronResourceServiceImpl) checkIfResourcePatchOperationValid(descriptorBean *bean.DtResObjectInternalDescBean,
	objectData string, newObjectData string, queries []bean.PatchQuery) error {
	return nil
}

//Create/Update(old method used for catalogue) resource object and related method ends
//

//
//PatchResObj dummy implementation

func (impl *DevtronResourceServiceImpl) PatchResObj(ctx context.Context, req *bean.DtResObjPatchReqBean, userId int32) (*bean.SuccessResponse, error) {
	return nil, fmt.Errorf("implementation not supported")
}

//Patch resource object and related method ends
//

//
//Delete resource object and related method starts

func (impl *DevtronResourceServiceImpl) DeleteResObj(ctx context.Context, req *bean.DtResObjDescApiBean, queryParams apiBean.GetResourceQueryParams, userId int32) (*bean.SuccessResponse, error) {
	//getting object
	commonDescBean := adapter.BuildDtResObjCommonDescBean(req.Kind, req.SubKind, req.Version, queryParams.Id, req.Name, queryParams.Identifier)
	internalDescriptorBean := adapter.BuildDtResObjIntDescBean(commonDescBean, req.SchemaId, queryParams.Component, userId)
	adapter.SetIdAndIdentifierInInternalDescriptorBean(internalDescriptorBean, queryParams.Id, queryParams.Identifier)
	_, existingObj, err := impl.getResourceSchemaAndExistingObject(internalDescriptorBean)
	if err != nil {
		impl.logger.Errorw("error in getting existing resource object", "err", err, "req", req)
		return nil, err
	}
	f := getFuncToValidateResObjDelete(req.Kind, req.SubKind, req.Version)
	if f != nil {
		isValid, err := f(impl, existingObj)
		if err != nil {
			impl.logger.Errorw("error in validation delete object request", "err", err, "req", req)
			return nil, err
		}
		if !isValid {
			impl.logger.Errorw("invalid delete request", "err", err, "req", req)
			return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidDeleteRequest, bean.InvalidDeleteRequest)
		}
	}

	tx, err := impl.dtResObjectRepository.StartTx()
	// Rollback tx on error.
	defer impl.dtResObjectRepository.RollbackTx(tx)
	if err != nil {
		impl.logger.Errorw("error in getting transaction, DeleteResObj", "err", err)
		return nil, err
	}

	if req.Kind == bean.DevtronResourceTenant.ToString() {
		//getting child mappings
		childMappings, err := impl.dtResObjRelationReadService.GetChildMappingsForAParent(existingObj.Id, existingObj.DevtronResourceSchemaId)
		if err != nil {
			impl.logger.Errorw("error in getting child mappings", "err", err)
			return nil, err
		}
		for _, childMapping := range childMappings {
			commonDescBeanChild := adapter.BuildDtResObjCommonDescBean(bean.DevtronResourceInstallation.ToString(), "", bean.DevtronResourceVersionAlpha1.ToString(),
				childMapping.ComponentObjectId, "", "")
			internalDescBeanChild := adapter.BuildDtResObjIntDescBean(commonDescBeanChild, childMapping.ComponentDtResSchemaId, nil, userId)
			adapter.SetIdAndIdentifierInInternalDescriptorBean(internalDescBeanChild, childMapping.ComponentObjectId, "")
			err = impl.dtResInternalProcessingService.DeleteObjectAndItsDep(internalDescBeanChild, tx)
			if err != nil {
				impl.logger.Errorw("error in DeleteResObj", "internalDescBeanChild", internalDescBeanChild)
				return nil, err
			}
		}
	}

	err = impl.dtResInternalProcessingService.DeleteObjectAndItsDep(internalDescriptorBean, tx)
	if err != nil {
		impl.logger.Errorw("error in DeleteResObj", "request", req)
		return nil, err
	}
	err = impl.dtResObjectRepository.CommitTx(tx)
	if err != nil {
		impl.logger.Errorw("error in committing transaction, DeleteResObj", "err", err)
		return nil, err
	}
	return adapter.GetSuccessPassResponse(), nil
}

//Delete resource object and related method ends
//

//
//Clone resource object and related method starts

// CloneResObj : clones a resource object
// step 1 : validate that if the new version requested is already present
// step 2 : validate if clone source is valid
// step 3 : get parent for clone request
// step 4 : remove/update fields from clone source object data
// step 5 : create new resource object
func (impl *DevtronResourceServiceImpl) CloneResObj(ctx context.Context, req *bean.DtResObjCloneReqBean, userId int32) error {
	createdOn := time.Now()
	//step 1 : validate if clone source is valid
	var cloneFromId int
	var cloneFromIdentifier string
	if req.CloneFrom != nil {
		cloneFromId = req.CloneFrom.Id
		cloneFromIdentifier = req.CloneFrom.Identifier
	}
	//using kind, subKind, version from request and not from cloneFrom because this should be same with where getting cloned
	commonDescriptorBean := adapter.BuildDtResObjCommonDescBean(req.Kind, req.SubKind, req.Version, cloneFromId, "", cloneFromIdentifier)
	cloneFromDescriptorBean := adapter.BuildDtResObjIntDescBean(commonDescriptorBean, 0, nil, userId)

	schemaObj, cloneFromObj, err := impl.getResourceSchemaAndExistingObject(cloneFromDescriptorBean)
	if err != nil {
		impl.logger.Errorw("error in getResourceSchemaAndExistingObject", "err", err, "cloneFromDescriptorBean", cloneFromDescriptorBean)
		return err
	}
	if cloneFromObj == nil || cloneFromObj.Id == 0 {
		impl.logger.Errorw("error in CloneResObj, clone source does not exists", "request", req.DtResObjDescApiBean)
		return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.CloneSourceDoesNotExistsErrMessage, bean.CloneSourceDoesNotExistsErrMessage)
	}
	//get all dependency mappings of this cloneSource object
	cloneSourceDepMappings, err := impl.dtResObjRelationReadService.GetAllMappingsForObj(cloneFromObj.Id, cloneFromObj.DevtronResourceSchemaId)
	if err != nil {
		impl.logger.Errorw("error in GetAllMappingsForObj", "req", req, "err", err)
		return err
	}
	//step 2 : get parent from clone source
	parentConfig := &bean.ResourceIdentifier{DtResTypeReq: bean.DtResTypeReq{}}
	for _, cloneSrcMapping := range cloneSourceDepMappings {
		if cloneSrcMapping.TypeOfDependency == bean.DevtronResourceDependencyTypeParent.ToString() {
			parentConfig.Id = cloneSrcMapping.DependencyObjectId
			parentConfig.SchemaId = cloneSrcMapping.DependencyDtResSchemaId
			kind, subKind, version, err := helper.GetKindSubKindAndVersionOfResourceBySchemaId(cloneSrcMapping.DependencyDtResSchemaId,
				impl.dtResReadService.GetDtResourcesSchemaByIdMap(), impl.dtResReadService.GetDtResourcesByIdMap())
			if err != nil {
				return err
			}
			parentConfig.ResourceKind = bean.DtResKind(kind)
			parentConfig.ResourceSubKind = bean.DtResKind(subKind)
			parentConfig.ResourceVersion = bean.DtResVersion(version)
		}
	}
	internalDescriptorBean := adapter.BuildIntDescBeanFromApiBean(req.DtResObjDescApiBean, userId)
	internalCloneReq := adapter.BuildInternalCloneReqBeanFromApiBean(req, internalDescriptorBean, userId)
	//step 3 : validate that if the new version requested is already present
	funcToSetDefault := getFuncToSetDefaultValueAndValidateForCloneReq(req.Kind, req.SubKind, req.Version)
	if funcToSetDefault != nil {
		err = funcToSetDefault(impl, internalCloneReq, parentConfig)
		if err != nil {
			impl.logger.Errorw("err, SetDefaultValueAndValidateForCloneReq", "err", err, "req", req)
			return err
		}
	}
	_, existingResourceObjectFound, err := impl.getResourceSchemaAndCheckIfObjectFound(internalDescriptorBean)
	if err != nil {
		impl.logger.Errorw("error in getResourceSchemaAndCheckIfObjectFound", "err", err, "request", req.DtResObjDescApiBean)
		return err
	}
	if existingResourceObjectFound {
		impl.logger.Errorw("error in CloneResObj, request resource object already exists", "request", req.DtResObjDescApiBean)
		return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.ResourceAlreadyExistsMessage, bean.ResourceAlreadyExistsMessage)
	}
	//step 4 : remove/update fields from clone source object data
	replaceDataMap := make(map[string]interface{})
	funcToGetPathUpdateMap := getFuncToGetPathUpdateMapForCloneReq(req.Kind, req.SubKind, req.Version)
	if funcToGetPathUpdateMap != nil {
		replaceDataMap, err = funcToGetPathUpdateMap(impl, internalCloneReq, createdOn)
		if err != nil {
			impl.logger.Errorw("err, GetPathUpdateMapForCloneReq", "err", err, "req", req)
			return err
		}
	}

	//step 5 : create new resource object
	objectData := cloneFromObj.ObjectData
	for pathToBeUpdated, valueToBeUpdated := range replaceDataMap {
		objectData, err = helper.PatchResourceObjectDataAtAPath(objectData, pathToBeUpdated, valueToBeUpdated)
		if err != nil {
			impl.logger.Errorw("error in updating new data in clone source object", "err", err, "pathToBeUpdated", pathToBeUpdated, "valueToBeUpdated", valueToBeUpdated)
			return err
		}
	}
	devtronResourceObject := &repository.DevtronResourceObject{
		Identifier:              internalCloneReq.Identifier,
		DevtronResourceId:       schemaObj.DevtronResourceId,
		DevtronResourceSchemaId: schemaObj.Id,
		ObjectData:              objectData,
		Deleted:                 false,
		AuditLog: sql.AuditLog{
			CreatedBy: userId,
			CreatedOn: createdOn,
			UpdatedBy: userId,
			UpdatedOn: createdOn,
		},
	}
	// for IdType -> bean.ResourceObjectIdType; DevtronResourceObject.OldObjectId is not present
	if internalDescriptorBean.IdType != bean.ResourceObjectIdType {
		devtronResourceObject.OldObjectId = internalCloneReq.Id
	}
	tx, err := impl.dtResObjectRepository.StartTx()
	defer impl.dtResObjectRepository.RollbackTx(tx)
	if err != nil {
		impl.logger.Errorw("error encountered in db tx, CloneResObj", "err", err)
		return err
	}
	err = impl.dtResObjectRepository.Save(tx, devtronResourceObject)
	if err != nil {
		impl.logger.Errorw("error in saving", "err", err, "req", devtronResourceObject)
		return err
	}
	//updating mappings with new resourceObjectId and saving them
	newMappings := cloneSourceDepMappings
	for i := range newMappings {
		newMappings[i].Id = 0
		newMappings[i].ComponentObjectId = devtronResourceObject.Id
	}
	if len(newMappings) > 0 {
		err = impl.dtResObjDepsRelationsRepository.SaveInBatch(tx, newMappings)
		if err != nil {
			impl.logger.Errorw("error in saving dep mappings", "newMappings", newMappings, "err", err)
			return err
		}
	}
	//saving audit
	impl.dtResObjectAuditService.SaveAudit(devtronResourceObject, repository.AuditOperationTypeClone, nil)
	err = impl.dtResObjectRepository.CommitTx(tx)
	if err != nil {
		impl.logger.Errorw("error in committing tx CloneResObj", "err", err)
		return err
	}
	return nil
}

//Clone resource object and related method ends
//

//
//Get resource object dependencies : dummy implementation

func (impl *DevtronResourceServiceImpl) GetResDeps(req *bean.DtResObjDescApiBean, query *apiBean.GetDependencyQueryParams, userId int32) (*bean.DtResObjDepsReqBean, error) {
	return nil, fmt.Errorf("implementation not supported")
}

//Get resource object dependencies and related method ends
//

// Get dependency options starts
//

func (impl *DevtronResourceServiceImpl) GetDepOptions(ctx context.Context, req *bean.DtResObjDescApiBean, query *apiBean.GetDependencyOptionsQueryParams) ([]*bean.DepOptions, error) {
	depOptions, err := impl.getDepOptions(req, query)
	if err != nil {
		impl.logger.Errorw("error, getDepOptions", "req", req, "query", query, "err", err)
		return nil, err
	}
	return depOptions, nil
}

//
// Get dependency options ends

// Get dependency config option starts
//

func (impl *DevtronResourceServiceImpl) GetDepConfigOptions(req *bean.DtResObjDescApiBean, query *apiBean.GetConfigOptionsQueryParams, userId int32) (any, error) {
	return nil, fmt.Errorf("implementation not supported")
}

//
// GetDepConfigOptions ends

//
// Get GetDepOffendingState

func (impl *DevtronResourceServiceImpl) GetDepsOffendingState(req *bean.DtResObjDescApiBean, query *apiBean.GetDepsOffendingQueryParams, userId int32) (response *bean.DepOffendingGetApiBean, err error) {
	internalDescriptorBean := adapter.BuildIntDescBeanFromApiBean(req, userId)
	_, existingResourceObject, err := impl.getResourceSchemaAndExistingObject(internalDescriptorBean)
	if err != nil {
		impl.logger.Errorw("error encountered in GetDepsOffendingState", "internalDescriptorBean", internalDescriptorBean, "err", err)
		return nil, err
	}
	if existingResourceObject == nil || existingResourceObject.Id == 0 {
		impl.logger.Warnw("invalid GetDepsOffendingState, object not found", "req", req)
		return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.ResourceDoesNotExistMessage, bean.ResourceDoesNotExistMessage)
	}
	f := getFuncToGetDepsOffendingStateFuncMap(internalDescriptorBean.Kind, internalDescriptorBean.SubKind, internalDescriptorBean.Version)
	if f == nil {
		return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.UnimplementedResourceKindOrVersion, bean.UnimplementedResourceKindOrVersion)
	}
	response, err = f(impl, existingResourceObject, query, userId)
	if err != nil {
		impl.logger.Errorw("error encountered in GetDepsOffendingState", "id", existingResourceObject.Id, "err", err)
		return nil, err
	}
	return response, nil
}

//
// Get GetDepOffendingState ends

//
//Create/Update resource object dependencies and related method starts

func (impl *DevtronResourceServiceImpl) CreateOrUpdateResDeps(ctx context.Context, req *bean.DtResObjDepsReqBean, userId int32) error {
	// empty implementation as this is not supported in this service, extended differs
	return nil
}

func (impl *DevtronResourceServiceImpl) setDefaultDataAndValidateDeps(req *bean.DtResObjDepsReqInternalBean) error {
	if len(req.Dependencies) == 0 {
		impl.logger.Warnw("no dependency in request, setDefaultDataAndValidateDeps", "req", req)
	}
	if req.Id == 0 {
		_, devtronResourceSchema, err := impl.dtResReadService.GetResAndSchemaFromResType(adapter.BuildDtResTypeInternalReq(req.Kind, req.SubKind, req.Version))
		if err != nil {
			impl.logger.Errorw("error in getResourceSchemaAndCheckIfObjectFound, release track", "err", err)
			err = util.GetApiErrorAdapter(http.StatusNotFound, "404", bean.InvalidResourceKindOrVersion, bean.InvalidResourceKindOrVersion)
			return err
		}
		req.SchemaId = devtronResourceSchema.Id
		ids, err := impl.getDtResIdsFromIdentifiers([]string{req.Identifier}, devtronResourceSchema.Id)
		if err != nil {
			return err
		}
		if len(ids) == 1 {
			req.Id = ids[0]
		}
	}
	allDependenciesToBeValidated := make([]*bean.DtResDepJsonBean, 0, len(req.Dependencies)+2*len(req.ChildDependencies))
	levelTypeDepCount := 0
	isLevelPropagatedFromParent := true
	for i := range req.Dependencies {
		dep := req.Dependencies[i]
		adapter.RemoveRedundantFieldsAndSetDefaultForDependency(dep, false)
		if helper.CheckIfDependencyTypeToBeValidated(dep.TypeOfDependency) {
			allDependenciesToBeValidated = append(allDependenciesToBeValidated, dep)
		}
		if dep.TypeOfDependency.IsLevelTypeDep() {
			levelTypeDepCount++
			isLevelPropagatedFromParent = dep.IsLevelPropagatedFromParent
		}
	}
	for j := range req.ChildDependencies {
		childDependency := req.ChildDependencies[j]
		adapter.RemoveRedundantFieldsAndSetDefaultForDependency(childDependency, true)
		if helper.CheckIfDependencyTypeToBeValidated(childDependency.TypeOfDependency) {
			allDependenciesToBeValidated = append(allDependenciesToBeValidated, childDependency)
		}
		//here assuming that dependencies of childDependency further don't have their own dependencies, i.e. only one level of nesting in resources
		for k := range childDependency.Dependencies {
			depOfChildDep := childDependency.Dependencies[k]
			adapter.RemoveRedundantFieldsAndSetDefaultForDependency(depOfChildDep, false)
			if helper.CheckIfDependencyTypeToBeValidated(depOfChildDep.TypeOfDependency) {
				allDependenciesToBeValidated = append(allDependenciesToBeValidated, depOfChildDep)
			}
		}
	}

	mapOfSchemaIdAndDependencyData := make(map[int]bean.IdIdentifierIndex)
	for i, dependency := range allDependenciesToBeValidated {
		err := impl.validateDepResourceType(allDependenciesToBeValidated[i])
		if err != nil {
			return err
		}

		allDependenciesToBeValidated[i].IdType = bean.OldObjectId // currently only apps, cd pipelines & environments are expected in dependencies PUT request
		if dependency.Id > 0 {
			data := mapOfSchemaIdAndDependencyData[dependency.DevtronResourceSchemaId]
			data.Ids = append(data.Ids, dependency.Id)
			data.IdsIndex = append(data.IdsIndex, i)
			mapOfSchemaIdAndDependencyData[dependency.DevtronResourceSchemaId] = data
		} else {
			data := mapOfSchemaIdAndDependencyData[dependency.DevtronResourceSchemaId]
			data.Identifiers = append(data.Identifiers, dependency.Identifier)
			data.IdentifiersIndex = append(data.IdentifiersIndex, i)
			mapOfSchemaIdAndDependencyData[dependency.DevtronResourceSchemaId] = data
		}
	}
	internalMessage := ""
	userMessage := ""
	isRequestInvalid := false

	invalidSchemaIds := make([]int, 0, len(mapOfSchemaIdAndDependencyData))
	var invalidAppIds []int
	var invalidAppNames []string
	var invalidCdPipelineIds []int
	var invalidEnvIds []int
	var invalidEnvNames []string
	var err error
	for devtronResourceSchemaId, depData := range mapOfSchemaIdAndDependencyData {
		if devtronResourceSchema, ok := impl.dtResReadService.GetDtResourcesSchemaByIdMap()[devtronResourceSchemaId]; ok {
			switch devtronResourceSchema.DevtronResource.Kind {
			case bean.DevtronResourceDevtronApplication.ToString():
				var mapOfAppNameId map[string]int
				var mapOfAppIdName map[int]string
				mapOfAppNameId, mapOfAppIdName, invalidAppIds, invalidAppNames, err = impl.getAppsMapAndReturnNotFoundIdsAndNames(depData.Ids, depData.Identifiers)
				if err != nil {
					impl.logger.Errorw("error, getAppsAndReturnNotFoundIds", "err", err, "appIds", depData.Ids)
					return err
				}
				// not doing this for release-track as we keep deps with identifiers
				if len(invalidAppIds) == 0 && len(invalidAppNames) == 0 && !helper.CheckIfKindRTWithVersionAlpha1(req.Kind, req.Version) {
					for i, identifier := range depData.Identifiers {
						dependencyIndexToBeUpdated := depData.IdentifiersIndex[i]
						allDependenciesToBeValidated[dependencyIndexToBeUpdated].Identifier = ""
						allDependenciesToBeValidated[dependencyIndexToBeUpdated].Id = mapOfAppNameId[identifier]
					}
				} else if len(invalidAppIds) == 0 && len(invalidAppNames) == 0 && helper.CheckIfKindRTWithVersionAlpha1(req.Kind, req.Version) {
					for i, id := range depData.Ids {
						dependencyIndexToBeUpdated := depData.IdsIndex[i]
						allDependenciesToBeValidated[dependencyIndexToBeUpdated].Id = 0
						allDependenciesToBeValidated[dependencyIndexToBeUpdated].IdType = ""
						allDependenciesToBeValidated[dependencyIndexToBeUpdated].Identifier = mapOfAppIdName[id]
					}
				}
			case bean.DevtronResourceCdPipeline.ToString():
				if len(depData.Identifiers) > 0 {
					isRequestInvalid = true
					internalMessage += fmt.Sprintf("identifiers not supported in cd pipelines as of now : %v\n", depData.Identifiers)
					userMessage = bean.MappingFailedMessage
				}
				pipelineIds := depData.Ids
				invalidCdPipelineIds, err = impl.getCdPipelinesAndReturnNotFoundIds(pipelineIds)
				if err != nil {
					impl.logger.Errorw("error, getCdPipelinesAndReturnNotFoundIds", "err", err, "pipelineIds", pipelineIds)
					return err
				}
			case bean.DevtronResourceEnvironment.ToString():
				var mapOfEnvNameId map[string]int
				var envCount *bean.EnvCount
				mapOfEnvNameId, invalidEnvIds, invalidEnvNames, envCount, err = impl.getEnvsMapAndReturnNotFoundIdsAndNames(depData.Ids, depData.Identifiers)
				if err != nil {
					impl.logger.Errorw("error, getEnvsMapAndReturnNotFoundIdsAndNames", "err", err, "envIds", depData.Ids, "envNames", depData.Identifiers)
					return err
				}
				if envCount.NonVirtualEnvCount != 0 && envCount.TotalCount != envCount.NonVirtualEnvCount {
					return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.RequestFailedForInstallation, bean.RequestFailedForInstallation)
				} else if envCount.VirtualEnvCount != 0 && envCount.TotalCount != envCount.VirtualEnvCount {
					return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.RequestFailedForInstallation, bean.RequestFailedForInstallation)
				}
				if len(invalidEnvIds) == 0 && len(invalidEnvNames) == 0 {
					if req.Kind == bean.DevtronResourceInstallation.ToString() {
						allEnvIds := make([]int, 0, len(mapOfEnvNameId))
						for _, val := range mapOfEnvNameId {
							allEnvIds = append(allEnvIds, val)
						}
						count, err := impl.dtResObjRelationReadService.GetInstallationMappingCountForEnvValidation(allEnvIds, req.Id)
						if err != nil {
							impl.logger.Errorw("error, GetInstallationMappingCountForEnv", "err", err)
							return err
						}
						if count > 0 {
							isRequestInvalid = true
							internalMessage += bean.EnvAlreadyMappedToInstallationMessage
							userMessage = bean.EnvAlreadyMappedToInstallationMessage
							continue
						}
					}
					for i, identifier := range depData.Identifiers {
						dependencyIndexToBeUpdated := depData.IdentifiersIndex[i]
						allDependenciesToBeValidated[dependencyIndexToBeUpdated].Identifier = ""
						allDependenciesToBeValidated[dependencyIndexToBeUpdated].Id = mapOfEnvNameId[identifier]
					}
				}
			default:
				invalidSchemaIds = append(invalidSchemaIds, devtronResourceSchemaId)
			}
		} else {
			invalidSchemaIds = append(invalidSchemaIds, devtronResourceSchemaId)
		}
	}
	if len(invalidSchemaIds) > 0 {
		isRequestInvalid = true
		userMessage = bean.MappingFailedMessage + ":invalid schema"
		internalMessage += fmt.Sprintf("invalid schemaIds : %v\n", invalidSchemaIds)
	}
	if len(invalidAppIds) > 0 && !helper.CheckIfKindRTWithVersionAlpha1(req.Kind, req.Version) {
		isRequestInvalid = true
		userMessage = bean.MappingFailedMessage + ":invalid apps"
		internalMessage += fmt.Sprintf("invalid appIds : %v\n", invalidAppIds)
	}
	if len(invalidAppNames) > 0 && !helper.CheckIfKindRTWithVersionAlpha1(req.Kind, req.Version) {
		isRequestInvalid = true
		userMessage = bean.MappingFailedMessage + ":invalid apps"
		internalMessage += fmt.Sprintf("invalid appNames : %v\n", invalidAppNames)
	}
	if len(invalidCdPipelineIds) > 0 {
		isRequestInvalid = true
		userMessage = bean.MappingFailedMessage + ":invalid cd pipelines"
		internalMessage += fmt.Sprintf("invalid cdPipelineIds : %v\n", invalidCdPipelineIds)
	}
	if len(invalidEnvIds) > 0 {
		isRequestInvalid = true
		userMessage = bean.MappingFailedMessage + ":invalid envs"
		internalMessage += fmt.Sprintf("invalid envIds : %v\n", invalidEnvIds)
	}
	if len(invalidEnvNames) > 0 {
		isRequestInvalid = true
		userMessage = bean.MappingFailedMessage + ":invalid envs"
		internalMessage += fmt.Sprintf("invalid envNames : %v\n", invalidEnvNames)
	}
	if helper.CheckIfKindReleaseWithVersionAlpha1(req.Kind, req.Version) && !isLevelPropagatedFromParent && levelTypeDepCount > 1 {
		isRequestInvalid = true
		userMessage = bean.InvalidRequestLevelReleaseOrderNotMaintained
		internalMessage += bean.InvalidRequestLevelReleaseOrderNotMaintained
	}
	if isRequestInvalid {
		return util.GetApiErrorAdapter(http.StatusBadRequest, "400", userMessage, internalMessage)
	}
	return nil
}

func (impl *DevtronResourceServiceImpl) getAppsMapAndReturnNotFoundIdsAndNames(appIds []int, appNames []string) (map[string]int, map[int]string, []int, []string, error) {
	invalidAppIds := make([]int, 0, len(appIds))
	invalidAppNames := make([]string, 0, len(appNames))
	mapOfAppIds := make(map[int]string)
	mapOfAppNames := make(map[string]int) //map of appName and its id
	apps, err := impl.appRepository.FindAppsByIdsOrNames(appIds, appNames)
	if err != nil {
		impl.logger.Errorw("error in getting apps by ids or names", "err", err, "ids", appIds, "names", appNames)
		return mapOfAppNames, mapOfAppIds, invalidAppIds, invalidAppNames, err
	}
	for _, app := range apps {
		mapOfAppIds[app.Id] = app.AppName
		mapOfAppNames[app.AppName] = app.Id
	}
	for _, appId := range appIds {
		if _, ok := mapOfAppIds[appId]; !ok {
			invalidAppIds = append(invalidAppIds, appId)
		}
	}
	for _, appName := range appNames {
		if _, ok := mapOfAppNames[appName]; !ok {
			invalidAppNames = append(invalidAppNames, appName)
		}
	}

	return mapOfAppNames, mapOfAppIds, invalidAppIds, invalidAppNames, nil
}

func (impl *DevtronResourceServiceImpl) getCdPipelinesAndReturnNotFoundIds(pipelineIds []int) ([]int, error) {
	invalidCdPipelineIds := make([]int, 0, len(pipelineIds))
	mapOfCdPipelines := make(map[int]*pipelineConfig.Pipeline)
	pipelines, err := impl.pipelineRepository.FindByIdsIn(pipelineIds)
	if err != nil {
		impl.logger.Errorw("error in getting cd pipelines by ids", "err", err, "ids", pipelineIds)
		return nil, err
	}
	for _, pipeline := range pipelines {
		mapOfCdPipelines[pipeline.Id] = pipeline
	}
	if len(mapOfCdPipelines) != len(pipelineIds) {
		for _, dependencyId := range pipelineIds {
			if _, ok := mapOfCdPipelines[dependencyId]; !ok {
				invalidCdPipelineIds = append(invalidCdPipelineIds, dependencyId)
			}
		}
	}
	return invalidCdPipelineIds, nil
}

func (impl *DevtronResourceServiceImpl) getEnvsMapAndReturnNotFoundIdsAndNames(envIds []int, envNames []string) (map[string]int, []int, []string, *bean.EnvCount, error) {
	invalidEnvIds := make([]int, 0, len(envIds))
	invalidEnvNames := make([]string, 0, len(envNames))
	mapOfEnvIds := make(map[int]bool, len(envIds)+len(envNames))
	mapOfEnvNames := make(map[string]int, len(envIds)+len(envNames)) //map of envName and its id
	if len(envIds) == 0 && len(envNames) == 0 {
		return mapOfEnvNames, invalidEnvIds, invalidEnvNames, nil, fmt.Errorf("invalid request, no arguments available")
	}
	//TODO: add check for installation mapping
	envs, err := impl.envService.FindByIdsAndNames(envIds, envNames)
	if err != nil {
		impl.logger.Errorw("error in getting envs by ids or names", "err", err, "ids", envIds, "envNames", envNames)
		return mapOfEnvNames, invalidEnvIds, invalidEnvNames, nil, err
	}
	virtualEnvCount := 0
	nonVirtualEnvCount := 0
	for _, env := range envs {
		mapOfEnvIds[env.Id] = true
		mapOfEnvNames[env.Environment] = env.Id
		if env.IsVirtualEnvironment {
			virtualEnvCount = virtualEnvCount + 1
		} else {
			nonVirtualEnvCount = nonVirtualEnvCount + 1
		}
	}
	for _, envId := range envIds {
		if _, ok := mapOfEnvIds[envId]; !ok {
			invalidEnvIds = append(invalidEnvIds, envId)
		}
	}
	for _, envName := range envNames {
		if _, ok := mapOfEnvNames[envName]; !ok {
			invalidEnvNames = append(invalidEnvNames, envName)
		}
	}

	return mapOfEnvNames, invalidEnvIds, invalidEnvNames, adapter.BuildEnvCount(virtualEnvCount, nonVirtualEnvCount, len(envs)), nil
}

func (impl *DevtronResourceServiceImpl) getUpdatedDepsRequestData(req *bean.DtResObjDepsReqInternalBean) ([]*bean.DtResObjDepsReqInternalBean,
	[]*repository.DevtronResourceSchema, map[int]*repository.DevtronResourceObject, error) {
	_, parentDevtronResourceSchema, err := impl.dtResReadService.GetResAndSchemaFromResType(adapter.BuildDtResTypeInternalReq(req.Kind, req.SubKind, req.Version))
	if err != nil {
		impl.logger.Errorw("error in getUpdatedDepsRequestData", "kind", req.Kind, "version", req.Version, "err", err)
		return nil, nil, nil, err
	}
	allRequests := make([]*bean.DtResObjDepsReqInternalBean, 0, len(req.ChildDependencies)+1)
	allRequestSchemas := make([]*repository.DevtronResourceSchema, 0, len(req.ChildDependencies)+1)
	allArgValues := make([]interface{}, 0, len(req.ChildDependencies)+1)
	allArgTypes := make([]string, 0, len(req.ChildDependencies)+1)
	devtronSchemaIdsForAllArgs := make([]int, 0, len(req.ChildDependencies)+1)
	depMapKeyRequestIndexMap := make(map[string]int, len(req.ChildDependencies)+1)
	for j := range req.ChildDependencies {
		childDependency := req.ChildDependencies[j]
		//adding info of parent dependency in this child dependency's dependencies
		childDependency.Dependencies = append(childDependency.Dependencies, &bean.DtResDepJsonBean{
			Id:                      req.Id,
			DevtronResourceSchemaId: parentDevtronResourceSchema.Id,
			TypeOfDependency:        bean.DevtronResourceDependencyTypeParent,
		})

		//getting devtronResourceSchema for this child dependency
		devtronResourceSchema := impl.dtResReadService.GetDtResourcesSchemaByIdMap()[childDependency.DevtronResourceSchemaId]
		kind, subKind := helper.GetKindSubKindOfResourceBySchemaObject(devtronResourceSchema, impl.dtResReadService.GetDtResourcesByIdMap())
		reqForChildDependency := &bean.DtResObjDepsReqInternalBean{
			DtResObjectInternalDescBean: adapter.BuildDtResObjIntDescBean(adapter.BuildDtResObjCommonDescBean(kind, subKind, devtronResourceSchema.Version, childDependency.Id, "", ""), childDependency.DevtronResourceSchemaId, nil, req.UserId),
			DepsAndChildDepsJsonBean:    &bean.DepsAndChildDepsJsonBean{Dependencies: childDependency.Dependencies},
		}

		depMapKeyRequestIndexMap[helper.GetKeyForADependencyMapNew(reqForChildDependency.Id, devtronResourceSchema.Id, reqForChildDependency.Identifier)] = len(allRequests)
		allRequestSchemas = append(allRequestSchemas, devtronResourceSchema)
		allRequests = append(allRequests, reqForChildDependency)

		//need to add this child dependency in parent
		childDependency.Dependencies = nil //since we only need to add child dependency for parent-child relationship and not keeping nested dependencies in every object
		childDependency.TypeOfDependency = bean.DevtronResourceDependencyTypeChild
		req.Dependencies = append(req.Dependencies, childDependency)

		//adding oldObjectIds or names for getting existing objects
		appendDbObjectArgDetails(&allArgValues, &allArgTypes, &devtronSchemaIdsForAllArgs, childDependency.Id, childDependency.DevtronResourceSchemaId, childDependency.Identifier, false)
	}
	req.SchemaId = parentDevtronResourceSchema.Id
	//adding oldObjectId and Name for main request
	isTypeReleaseReleaseTrackOrInstallation := req.Kind == bean.DevtronResourceRelease.ToString() || req.Kind == bean.DevtronResourceInstallation.ToString() || req.Kind == bean.DevtronResourceReleaseTrack.ToString()
	depMapKeyRequestIndexMap[helper.GetKeyForADependencyMapNew(req.Id, parentDevtronResourceSchema.Id, req.Identifier)] = len(allRequests) //using len because have not appended yet
	//adding our initial request to allRequest
	allRequests = append(allRequests, req)
	appendDbObjectArgDetails(&allArgValues, &allArgTypes, &devtronSchemaIdsForAllArgs, req.Id, parentDevtronResourceSchema.Id, req.Identifier, isTypeReleaseReleaseTrackOrInstallation)
	allRequestSchemas = append(allRequestSchemas, parentDevtronResourceSchema)
	existingObjectsMap, err := impl.getExistingObjectsMap(allArgValues, allArgTypes, devtronSchemaIdsForAllArgs, depMapKeyRequestIndexMap, isTypeReleaseReleaseTrackOrInstallation)
	if err != nil {
		impl.logger.Errorw("error, getExistingObjectsMap", "err", err)
		return nil, nil, nil, err
	}
	return allRequests, allRequestSchemas, existingObjectsMap, nil
}

func (impl *DevtronResourceServiceImpl) getExistingObjectsMap(allArgValues []interface{}, allArgTypes []string, devtronSchemaIdsForAllArgs []int, depMapKeyRequestIndexMap map[string]int, isTypeReleaseReleaseTrackOrInstallation bool) (map[int]*repository.DevtronResourceObject, error) {
	existingObjectsMap := make(map[int]*repository.DevtronResourceObject, len(allArgValues))
	if len(allArgValues) > 0 {
		oldObjects, err := impl.dtResObjectRepository.GetObjectsByArgAndSchemaIds(allArgValues, allArgTypes, devtronSchemaIdsForAllArgs)
		if err != nil && err != pg.ErrNoRows {
			impl.logger.Errorw("error in getting devtron schemas by old object id or name and schema id array", "err", err,
				"allArgValues", allArgValues, "allArgTypes", allArgTypes, "schemaIds", devtronSchemaIdsForAllArgs)
			return nil, err
		}
		for _, oldObject := range oldObjects {
			kind, _, _, err := helper.GetKindSubKindAndVersionOfResourceBySchemaId(oldObject.DevtronResourceSchemaId, impl.dtResReadService.GetDtResourcesSchemaByIdMap(), impl.dtResReadService.GetDtResourcesByIdMap())
			if err != nil {
				impl.logger.Errorw("error encountered in getExistingObjectsMap", "DevtronResourceSchemaId", oldObject.DevtronResourceSchemaId, "err", err)
				return nil, err
			}
			idForDepMap := 0
			if adapter.GetIdTypeBasedOnKind(kind) == bean.ResourceObjectIdType {
				idForDepMap = oldObject.Id
			} else {
				idForDepMap = oldObject.OldObjectId
			}
			var index int
			var ok bool
			if index, ok = depMapKeyRequestIndexMap[helper.GetKeyForADependencyMapNew(idForDepMap, oldObject.DevtronResourceSchemaId, "")]; !ok {
				index, ok = depMapKeyRequestIndexMap[helper.GetKeyForADependencyMapNew(0, oldObject.DevtronResourceSchemaId, oldObject.Identifier)]
			}
			existingObjectsMap[index] = oldObject
		}
	}
	if isTypeReleaseReleaseTrackOrInstallation {
		if _, ok := existingObjectsMap[len(allArgValues)-1]; !ok {
			return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.ResourceDoesNotExistMessage, bean.ResourceDoesNotExistMessage)
		}
	}
	return existingObjectsMap, nil
}

func (impl *DevtronResourceServiceImpl) validateDepResourceType(dep *bean.DtResDepJsonBean) error {
	isValidDependencyResourceType := false
	//first checking the schemaId
	_, ok2 := impl.dtResReadService.GetDtResourcesSchemaByIdMap()[dep.DevtronResourceSchemaId]
	isValidDependencyResourceType = ok2
	if !isValidDependencyResourceType { //ids not valid, check if data received from resourceType is correct
		_, dtSchema, err := impl.dtResReadService.GetResAndSchemaFromResType(dep.DtResTypeInternalReq)
		if err != nil {
			//both ids and resource type data invalid
			return err
		}
		//valid resource type data, set ids
		dep.DevtronResourceSchemaId = dtSchema.Id
	}
	return nil
}

func appendDbObjectArgDetails(argValues *[]interface{}, argTypes *[]string, schemaIds *[]int, id, schemaId int, identifier string, isTypeReleaseReleaseTrackOrInstallation bool) {
	var argValue interface{}
	var argType string
	if id > 0 {
		argValue = id
		if isTypeReleaseReleaseTrackOrInstallation {
			argType = bean.IdDbColumnKey
		} else {
			argType = bean.OldObjectIdDbColumnKey
		}
	} else {
		argValue = fmt.Sprintf("'%s'", identifier) //quoting because postgres query will fail since we've made it generic
		argType = bean.IdentifierDbColumnKey
	}
	*argValues = append(*argValues, argValue)
	*argTypes = append(*argTypes, argType)
	*schemaIds = append(*schemaIds, schemaId)
}

//Create/Update resource object dependencies and related method ends
//

//
//PatchResDeps: dummy implementation

func (impl *DevtronResourceServiceImpl) PatchResDeps(ctx context.Context, req *bean.DtResDepPatchAPIBean, userId int32) (*bean.SuccessResponse, error) {
	return nil, fmt.Errorf("implementation not supported")
}

//Patch resource object dependencies and related method ends
//

//Get resource summary : dummy implementation
//

func (impl *DevtronResourceServiceImpl) GetResSummary(ctx context.Context, req *bean.DtResObjDescApiBean) (*bean.DtResObjSummaryResp, error) {
	return nil, fmt.Errorf("implementation not supported")
}

//
//Get resource summary and related method ends

//
// create update common methods starts

func (impl *DevtronResourceServiceImpl) createOrUpdateDevResObj(ctx context.Context, tx *pg.Tx, requirementReq *bean.ResObjRequirementReq, devtronResourceSchema *repository.DevtronResourceSchema, devtronResourceObject *repository.DevtronResourceObject, auditPaths []string) (finalResObj *repository.DevtronResourceObject, err error) {
	newCtx, span := otel.Tracer("DevtronResourceService").Start(ctx, "createOrUpdateDevResObj")
	defer span.End()
	isReqTxNil := tx == nil
	if isReqTxNil {
		tx, err = impl.dtResObjectRepository.StartTx()
		defer impl.dtResObjectRepository.RollbackTx(tx)
		if err != nil {
			impl.logger.Errorw("error encountered in db tx, createOrUpdateDevResObj", "err", err)
			return finalResObj, err
		}
	}
	reqBean := requirementReq.ReqBean
	objectDataPath := requirementReq.ObjectDataPath
	skipJsonSchemaValidation := requirementReq.SkipJsonSchemaValidation
	var objectDataGeneral string
	schema := ""
	if devtronResourceSchema != nil {
		schema = devtronResourceSchema.Schema
	}
	devtronResourceObjectPresentAlready := devtronResourceObject != nil && devtronResourceObject.Id > 0
	initialObjectData := ""
	if devtronResourceObjectPresentAlready {
		initialObjectData = devtronResourceObject.ObjectData
	}

	if reqBean.ObjectData != "" {
		//we need to put the object got from UI at a path(possible values currently - overview.metadata or dependencies) since only this part is controlled from UI currently
		objectDataGeneral, err = sjson.Set(initialObjectData, objectDataPath, json.RawMessage(reqBean.ObjectData))
		if err != nil {
			impl.logger.Errorw("error in setting version in schema", "err", err, "request", reqBean)
			return finalResObj, err
		}
	}
	objectDataGeneral, err = impl.setDtManagedFieldsInObjectData(objectDataGeneral, reqBean)
	if err != nil {
		impl.logger.Errorw("error, setDtManagedFieldsInObjectData", "err", err, "req", reqBean)
		return finalResObj, err
	}
	objectDataGeneral, err = impl.setUserProvidedFieldsInObjectData(objectDataGeneral, reqBean)
	if err != nil {
		impl.logger.Errorw("error, setUserProvidedFieldsInObjectData", "err", err, "req", reqBean)
		return finalResObj, err
	}

	// below check is added because it might be possible that user might not have added catalog data and only updating dependencies.
	// In this case, the validation for catalog data will fail.
	if !skipJsonSchemaValidation {
		//validate user provided json with the schema
		result, err := helper.ValidateSchemaAndObjectData(schema, objectDataGeneral)
		if err != nil {
			impl.logger.Errorw("error in validating resource object json against schema", "result", result, "request", reqBean, "schema", schema, "objectData", objectDataGeneral)
			return finalResObj, err
		}
	}
	var auditAction repository.AuditOperationType
	if devtronResourceObjectPresentAlready {
		//object already exists, update the same
		devtronResourceObject.ObjectData = objectDataGeneral
		if len(devtronResourceObject.Identifier) == 0 {
			reqBean.Identifier, err = impl.getResourceObjectIdentifier(reqBean.Kind, reqBean.SubKind, reqBean.Version, devtronResourceObject)
			if err != nil {
				impl.logger.Errorw("error in building identifier for ResourceObject", "err", err,
					"kind", reqBean.Kind, "subKind", reqBean.SubKind, "Version", reqBean.Version, "oldObjectId", devtronResourceObject.OldObjectId)
				return finalResObj, err
			}
			devtronResourceObject.Identifier = reqBean.Identifier
		}
		devtronResourceObject.UpdateAuditLog(reqBean.UserId)
		err = impl.dtResObjectRepository.Update(tx, devtronResourceObject)
		if err != nil {
			impl.logger.Errorw("error in updating", "err", err, "req", devtronResourceObject)
			return finalResObj, err
		}
		auditAction = repository.AuditOperationTypeUpdate
	} else {
		var depRelations []*repository.DevtronResourceObjectDependencyRelations
		if reqBean.ParentConfig != nil {
			err = helper.UpdateKindAndSubKindParentConfig(reqBean.ParentConfig)
			if err != nil {
				impl.logger.Errorw("error in updating kind, subKind for parent resource config", "err", err)
				return finalResObj, err
			}
			objectDataGeneral, depRelations, err = impl.addParentDepToChildResObj(newCtx, reqBean, objectDataGeneral)
			if err != nil {
				impl.logger.Errorw("error in updating parent resource object", "err", err, "parentConfig", reqBean.ParentConfig)
				return finalResObj, err
			}
		}
		// this is handled for creation only, update is supported by patch, modify this when support is added for update(installation)
		f := getFuncToHandleSubscriptionConfigInCreateReq(reqBean.Kind, reqBean.SubKind, reqBean.Version)
		if f != nil {
			var depRelationToBeAdded *repository.DevtronResourceObjectDependencyRelations
			objectDataGeneral, depRelationToBeAdded, err = f(impl, newCtx, reqBean, devtronResourceSchema.Id, objectDataGeneral)
			if err != nil {
				impl.logger.Errorw("error implementing HandleSubscriptionConfigInCreateReq", "err", err, "subscriptionConfig", reqBean.SubscriptionConfig)
				return finalResObj, err
			}
			if depRelationToBeAdded != nil {
				depRelations = append(depRelations, depRelationToBeAdded)
			}

		} else {
			//not returning since all resource type do not have subscription config to handle
		}

		//object does not exist, create new
		devtronResourceObject = &repository.DevtronResourceObject{
			DevtronResourceId:       devtronResourceSchema.DevtronResourceId,
			DevtronResourceSchemaId: devtronResourceSchema.Id,
			ObjectData:              objectDataGeneral,
		}
		// for IdType -> bean.ResourceObjectIdType; DevtronResourceObject.OldObjectId is not present
		if reqBean.IdType != bean.ResourceObjectIdType {
			devtronResourceObject.OldObjectId = reqBean.Id
		}
		reqBean.Identifier, err = impl.getResourceObjectIdentifier(reqBean.Kind, reqBean.SubKind, reqBean.Version, devtronResourceObject)
		if err != nil {
			impl.logger.Errorw("error in building identifier for ResourceObject", "err", err,
				"kind", reqBean.Kind, "subKind", reqBean.SubKind, "Version", reqBean.Version, "oldObjectId", devtronResourceObject.OldObjectId)
			return finalResObj, err
		}
		devtronResourceObject.Identifier = reqBean.Identifier
		devtronResourceObject.CreateAuditLog(reqBean.UserId)
		err = impl.dtResObjectRepository.Save(tx, devtronResourceObject)
		if err != nil {
			impl.logger.Errorw("error in saving", "err", err, "req", devtronResourceObject)
			return finalResObj, err
		}
		if len(depRelations) > 0 {
			//updating devtron resource object id for dep relations
			updateComponentObjectIdAndSchemaIdWithAuditInDepRelations(depRelations, devtronResourceObject.Id, devtronResourceObject.DevtronResourceSchemaId, reqBean.UserId)
			err = impl.dtResObjDepsRelationsRepository.SaveInBatch(tx, depRelations)
			if err != nil {
				impl.logger.Errorw("error, SaveInBatch", "err", err)
				return finalResObj, err
			}
		}
		auditAction = repository.AuditOperationTypeCreate
	}
	// handling default dev res obj ( for eg :- only one default release channel will exist in system)
	err = impl.handleDefaultValueForDevResObj(tx, reqBean, devtronResourceSchema.Id, []int{devtronResourceObject.Id})
	if err != nil {
		impl.logger.Errorw("error, handleDefaultValueForDevResObj", "err", err, "req", reqBean)
		return finalResObj, err
	}

	if isReqTxNil {
		//saving audit only when this is an atomic operation for other callee where tx is provided audit is handled there.
		impl.dtResObjectAuditService.SaveAudit(devtronResourceObject, auditAction, auditPaths)
		err = impl.dtResObjectRepository.CommitTx(tx)
		if err != nil {
			impl.logger.Errorw("error in committing tx createOrUpdateDevResObj", "err", err)
			return finalResObj, err
		}
	}
	// have set it here as in case of when devtronResourceObject came nil, we make new object so the callee will have this object reference
	finalResObj = devtronResourceObject
	return finalResObj, nil
}

func updateComponentObjectIdAndSchemaIdWithAuditInDepRelations(depRelations []*repository.DevtronResourceObjectDependencyRelations, drObjId, drSchemaId int, userId int32) {
	for _, relation := range depRelations {
		relation.ComponentObjectId = drObjId
		relation.ComponentDtResSchemaId = drSchemaId
		relation.CreatedOn = time.Now()
		relation.UpdatedOn = time.Now()
		relation.CreatedBy = userId
		relation.UpdatedBy = userId
	}
}

func (impl *DevtronResourceServiceImpl) setDtManagedFieldsInObjectData(objectData string, reqBean *bean.DtResObjInternalBean) (string, error) {
	var err error
	kindForSchema := reqBean.Kind
	if len(reqBean.SubKind) > 0 {
		kindForSchema += fmt.Sprintf("/%s", reqBean.SubKind)
	}
	objectData, err = sjson.Set(objectData, bean.KindKey, kindForSchema)
	if err != nil {
		impl.logger.Errorw("error in setting kind in schema", "err", err, "request", reqBean)
		return objectData, err
	}
	objectData, err = sjson.Set(objectData, bean.VersionKey, reqBean.Version)
	if err != nil {
		impl.logger.Errorw("error in setting version in schema", "err", err, "request", reqBean)
		return objectData, err
	}
	if reqBean.IdType != "" {
		objectData, err = sjson.Set(objectData, bean.ResourceObjectIdTypePath, reqBean.IdType)
		if err != nil {
			impl.logger.Errorw("error in setting id type in schema", "err", err, "request", reqBean)
			return objectData, err
		}
	}
	objectData, err = sjson.Set(objectData, bean.ResourceObjectIdPath, reqBean.DtResObjectInternalDescBean.Id)
	if err != nil {
		impl.logger.Errorw("error in setting id in schema", "err", err, "request", reqBean)
		return objectData, err
	}
	if reqBean.Name != "" {
		objectData, err = sjson.Set(objectData, bean.ResourceObjectNamePath, reqBean.Name)
		if err != nil {
			impl.logger.Errorw("error in setting id in schema", "err", err, "request", reqBean)
			return objectData, err
		}
	}
	return objectData, nil
}

func (impl *DevtronResourceServiceImpl) setUserProvidedFieldsInObjectData(objectData string, reqBean *bean.DtResObjInternalBean) (string, error) {
	var err error
	f := getFuncToSetUserProvidedDataInResourceObject(reqBean.Kind, reqBean.SubKind, reqBean.Version)
	if f != nil {
		objectData, err = f(impl, objectData, reqBean)
	}
	return objectData, err
}

func (impl *DevtronResourceServiceImpl) getParentDepsInResDepsInObjectData(reqBean *bean.DtResObjInternalBean, parentResourceObject *repository.DevtronResourceObject) ([]*bean.DtResDepJsonBean, []*repository.DevtronResourceObjectDependencyRelations, float64, error) {
	f := getFuncToGetParentDepsInResDeps(reqBean.Kind, reqBean.SubKind, reqBean.Version)
	if f != nil {
		return f(impl, parentResourceObject)
	}
	// max index is returned as one if implementation is not supported(default value)
	return nil, nil, 1, nil
}

func (impl *DevtronResourceServiceImpl) handleDefaultValueForDevResObj(tx *pg.Tx, reqBean *bean.DtResObjInternalBean, dRSchemaId int, notInIds []int) error {
	var err error
	f := getFuncToHandleDefaultDevResObj(reqBean.Kind, reqBean.SubKind, reqBean.Version)
	if f != nil {
		err = f(impl, tx, dRSchemaId, notInIds)
	}
	return err
}

func (impl *DevtronResourceServiceImpl) addParentDepToChildResObj(ctx context.Context, reqBean *bean.DtResObjInternalBean, objectDataGeneral string) (string, []*repository.DevtronResourceObjectDependencyRelations, error) {
	newCtx, span := otel.Tracer("DevtronResourceService").Start(ctx, "addParentDependencyToChildResourceObj")
	defer span.End()
	parentResourceObject, err := impl.getResourceObjectForResourceIdentifier(newCtx, reqBean.ParentConfig)
	if err != nil {
		impl.logger.Errorw("error in getting parent resource object by id or name", "err", err, "parentConfig", reqBean.ParentConfig)
		return objectDataGeneral, nil, err
	}

	depsToBeAdded, depRelationsToBeAdded, maxIndex, err := impl.getParentDepsInResDepsInObjectData(reqBean, parentResourceObject)
	if err != nil {
		impl.logger.Errorw("error, getParentDepsInResDepsInObjectData", "err", err)
		return objectDataGeneral, nil, err
	}
	// generate dependency data
	parentObjectId, parentIdType := helper.GetResourceObjectIdAndType(parentResourceObject)
	parentDependency := adapter.CreateInternalDepData(parentObjectId, parentResourceObject.DevtronResourceSchemaId, maxIndex, bean.DevtronResourceDependencyTypeParent, parentIdType)

	parentDepRelation := &repository.DevtronResourceObjectDependencyRelations{
		DependencyObjectId:      parentObjectId,
		DependencyDtResSchemaId: parentResourceObject.DevtronResourceSchemaId,
		TypeOfDependency:        bean.DevtronResourceDependencyTypeParent.ToString(),
	}
	allDependencies := append(depsToBeAdded, parentDependency)
	depRelations := append(depRelationsToBeAdded, parentDepRelation)
	// patch updated dependency data
	objectDataGeneral, err = sjson.Set(objectDataGeneral, bean.ResourceObjectDependenciesPath, allDependencies)
	if err != nil {
		impl.logger.Errorw("error in setting parent dependencies in child resource object", "err", err, "parentDependency", parentDependency)
		return objectDataGeneral, nil, err
	}

	return objectDataGeneral, depRelations, nil
}

func (impl *DevtronResourceServiceImpl) getResourceObjectForResourceIdentifier(ctx context.Context, parentConfig *bean.ResourceIdentifier) (*repository.DevtronResourceObject, error) {
	_, span := otel.Tracer("DevtronResourceService").Start(ctx, "getResourceObjectForResourceIdentifier")
	defer span.End()
	if parentConfig == nil {
		return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.ResourceParentConfigDataNotFound, bean.ResourceParentConfigDataNotFound)
	}
	var resourceSchema *repository.DevtronResourceSchema
	var err error
	var ok bool
	if parentConfig.SchemaId > 0 {
		resourceSchema, ok = impl.dtResReadService.GetDtResourcesSchemaByIdMap()[parentConfig.SchemaId]
		if !ok {
			impl.logger.Errorw("error in getting parent devtronResourceSchema", "err", err, "kind", parentConfig.ResourceKind, "subKind", parentConfig.ResourceSubKind, "version", parentConfig.ResourceVersion)
			err = util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceKindOrVersion, bean.InvalidResourceKindOrVersion)
			return nil, err
		}
	} else {
		_, resourceSchema, err = impl.dtResReadService.GetResAndSchemaFromResType(adapter.BuildDtResTypeInternalReq(parentConfig.ResourceKind.ToString(), parentConfig.ResourceSubKind.ToString(), parentConfig.ResourceVersion.ToString()))
		if err != nil {
			impl.logger.Errorw("error in getting parent devtronResourceSchema", "err", err, "kind", parentConfig.ResourceKind, "subKind", parentConfig.ResourceSubKind, "version", parentConfig.ResourceVersion)
			return nil, err
		}
	}
	if parentConfig.Id > 0 {
		parentResourceObject, err := impl.dtResObjectRepository.FindByIdAndSchemaId(parentConfig.Id, resourceSchema.Id)
		if err != nil {
			impl.logger.Errorw("error in getting object by id or name", "err", err, "id", parentConfig.Id)
			if util.IsErrNoRows(err) {
				err = util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceParentConfigId, bean.InvalidResourceParentConfigId)
			}
		}
		return parentResourceObject, err
	} else if len(parentConfig.Identifier) > 0 {
		parentResourceObject, err := impl.dtResObjectRepository.FindByObjectIdentifier(parentConfig.Identifier, resourceSchema.Id)
		if err != nil {
			impl.logger.Errorw("error in getting object by id or name", "err", err, "id", parentConfig.Id)
			if util.IsErrNoRows(err) {
				err = util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceParentConfigId, bean.InvalidResourceParentConfigId)
			}
		}
		return parentResourceObject, err
	} else {
		return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceParentConfigData, bean.InvalidResourceParentConfigData)
	}
}

func getExistingDepIdsForResType(childDependenciesOfParent []*bean.DtResDepJsonBean, devtronResourceSchemaId int) ([]int, float64) {
	dependenciesPresentAlready := make([]int, 0, len(childDependenciesOfParent))
	var maxIndex float64
	for _, childDependency := range childDependenciesOfParent {
		maxIndex = math.Max(maxIndex, float64(childDependency.Index))
		if childDependency.DevtronResourceSchemaId == devtronResourceSchemaId {
			dependenciesPresentAlready = append(dependenciesPresentAlready, childDependency.Id)
		}
	}
	return dependenciesPresentAlready, maxIndex
}

// create update common methods ends
//

//
// common helper methods starts

func (impl *DevtronResourceServiceImpl) getDtResIdsFromIdentifiers(identifiers []string, schemaId int) ([]int, error) {
	ids, err := impl.dtResObjectRepository.GetIdsByIdentifiers(identifiers, schemaId)
	if err != nil {
		impl.logger.Errorw("error encountered in getDevtronResourceIdsFromIdentifiers", "err", err, "identifiers", identifiers)
		return nil, err
	}
	return ids, nil
}

func (impl *DevtronResourceServiceImpl) getResourceSchemaAndExistingObject(req *bean.DtResObjectInternalDescBean) (*repository.DevtronResourceSchema, *repository.DevtronResourceObject, error) {
	var resourceSchema *repository.DevtronResourceSchema
	var err error
	var ok bool
	if req.SchemaId != 0 {
		resourceSchema, ok = impl.dtResReadService.GetDtResourcesSchemaByIdMap()[req.SchemaId]
		if !ok {
			err = util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceKindOrVersion, bean.InvalidResourceKindOrVersion)
		}
	} else {
		_, resourceSchema, err = impl.dtResReadService.GetResAndSchemaFromResType(adapter.BuildDtResTypeInternalReq(req.Kind, req.SubKind, req.Version))
	}
	if err != nil {
		impl.logger.Errorw("error in getting devtronResourceSchema", "err", err, "request", req)
		if util.IsErrNoRows(err) {
			err = util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceKindOrVersion, bean.InvalidResourceKindOrVersion)
		}
		return nil, nil, err
	}
	existingResourceObject, err := impl.getExistingDtObjBasedOnIdType(req.Id, resourceSchema.Id, req.Identifier, req.IdType)
	if err != nil {
		impl.logger.Errorw("error in getting object by id or name", "err", err, "request", req)
		return resourceSchema, nil, err
	}
	if existingResourceObject == nil && slices2.Contains([]string{bean.DevtronResourceTenant.ToString(), bean.DevtronResourceReleaseTrack.ToString(), bean.DevtronResourceRelease.ToString(), bean.DevtronResourceInstallation.ToString(), bean.DevtronResourceReleaseChannel.ToString()}, req.Kind) && req.Version == bean.DevtronResourceVersionAlpha1.ToString() {
		return nil, nil, util.GetApiErrorAdapter(http.StatusNotFound, "404", bean.ResourceDoesNotExistMessage, bean.ResourceDoesNotExistMessage)
	}
	return resourceSchema, existingResourceObject, nil
}

// getExistingDtObj : this method gets existing object if present in the db.
// If not present, returns nil object along with nil error (pg.ErrNoRows error is handled in this method only)
func (impl *DevtronResourceServiceImpl) getExistingDtObj(id, oldObjectId, devtronResourceSchemaId int, identifier string) (*repository.DevtronResourceObject, error) {
	var existingResourceObject *repository.DevtronResourceObject
	var err error
	if id > 0 {
		existingResourceObject, err = impl.dtResObjectRepository.FindByIdAndSchemaId(id, devtronResourceSchemaId)
		if err != nil && err != pg.ErrNoRows {
			impl.logger.Errorw("error in getting object by id or name", "err", err, "id", id, "devtronResourceSchemaId", devtronResourceSchemaId)
			return nil, err
		}
	} else if oldObjectId > 0 {
		existingResourceObject, err = impl.dtResObjectRepository.FindByOldObjectId(oldObjectId, devtronResourceSchemaId)
		if err != nil && err != pg.ErrNoRows {
			impl.logger.Errorw("error in getting object by id or name", "err", err, "oldObjectId", oldObjectId, "devtronResourceSchemaId", devtronResourceSchemaId)
			return nil, err
		}
	} else if len(identifier) > 0 {
		existingResourceObject, err = impl.dtResObjectRepository.FindByObjectIdentifier(identifier, devtronResourceSchemaId)
		if err != nil && err != pg.ErrNoRows {
			impl.logger.Errorw("error in getting object by identifier", "err", err, "identifier", identifier, "devtronResourceSchemaId", devtronResourceSchemaId)
			return nil, err
		}
	}
	// migrate data for resource object identifier
	if existingResourceObject != nil {
		err = impl.migrateDataForResourceObjectIdentifier(existingResourceObject)
		if err != nil {
			impl.logger.Warnw("error in service migrateDataForResourceObjectIdentifier", "err", err, "existingResourceObjectId", existingResourceObject.Id)
		}
	}
	return existingResourceObject, nil
}

// getExistingDtObj : this method gets existing object if present in the db.
// If not present, returns nil object along with nil error (pg.ErrNoRows error is handled in this method only)
func (impl *DevtronResourceServiceImpl) getExistingDtObjBasedOnIdType(id, devtronResourceSchemaId int, identifier string, idType bean.IdType) (*repository.DevtronResourceObject, error) {
	var existingResourceObject *repository.DevtronResourceObject
	var err error
	if id > 0 && idType == bean.ResourceObjectIdType {
		existingResourceObject, err = impl.dtResObjectRepository.FindByIdAndSchemaId(id, devtronResourceSchemaId)
		if err != nil && err != pg.ErrNoRows {
			impl.logger.Errorw("error in getting object by id or name", "err", err, "id", id, "devtronResourceSchemaId", devtronResourceSchemaId)
			return nil, err
		}
	} else if id > 0 && idType == bean.OldObjectId {
		existingResourceObject, err = impl.dtResObjectRepository.FindByOldObjectId(id, devtronResourceSchemaId)
		if err != nil && err != pg.ErrNoRows {
			impl.logger.Errorw("error in getting object by id or name", "err", err, "id", id, "devtronResourceSchemaId", devtronResourceSchemaId)
			return nil, err
		}
	} else if len(identifier) > 0 {
		existingResourceObject, err = impl.dtResObjectRepository.FindByObjectIdentifier(identifier, devtronResourceSchemaId)
		if err != nil && err != pg.ErrNoRows {
			impl.logger.Errorw("error in getting object by identifier", "err", err, "identifier", identifier, "devtronResourceSchemaId", devtronResourceSchemaId)
			return nil, err
		}
	}
	// migrate data for resource object identifier
	if existingResourceObject != nil {
		err = impl.migrateDataForResourceObjectIdentifier(existingResourceObject)
		if err != nil {
			impl.logger.Warnw("error in service migrateDataForResourceObjectIdentifier", "err", err, "existingResourceObjectId", existingResourceObject.Id)
		}
	}
	return existingResourceObject, nil
}

func (impl *DevtronResourceServiceImpl) FindNumberOfApplicationsWithDependenciesMapped() (int, error) {
	resourceObjects, err := impl.dtResObjectRepository.FindAllObjects()
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error in fetching all resource objects", "err", err)
		return 0, err
	}
	if err == pg.ErrNoRows {
		return 0, util.GetApiErrorAdapter(http.StatusNotFound, "404", "no resource objects found", err.Error())
	}
	countOfApplicationsWithDependenciesMapped := 0
	for _, object := range resourceObjects {
		objectData := object.ObjectData
		dependencies, err := impl.getDepsInternalBeanInObjectDataFromJsonString(object.DevtronResourceSchemaId, objectData, true)
		if err != nil {
			impl.logger.Errorw("error in getting dependencies from json object", "err", err)
			return 0, err
		}
		if len(dependencies) > 0 {
			countOfApplicationsWithDependenciesMapped += 1
		}
	}
	return countOfApplicationsWithDependenciesMapped, nil
}

func (impl *DevtronResourceServiceImpl) getResourceObjectIdentifier(kind, subKind, version string, existingResourceObject *repository.DevtronResourceObject) (string, error) {
	f := getFuncToBuildIdentifierForResObj(kind, subKind, version) //getting function for component requested from UI
	if f == nil {
		return "", util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.UnimplementedResourceKindOrVersion, bean.UnimplementedResourceKindOrVersion)
	}
	return f(impl, existingResourceObject)
}

func (impl *DevtronResourceServiceImpl) migrateDataForResourceObjectIdentifier(existingResourceObject *repository.DevtronResourceObject) error {
	if len(existingResourceObject.Identifier) != 0 {
		return nil
	}
	kind, subKind, version, err := helper.GetKindSubKindAndVersionOfResourceBySchemaId(existingResourceObject.DevtronResourceSchemaId,
		impl.dtResReadService.GetDtResourcesSchemaByIdMap(), impl.dtResReadService.GetDtResourcesByIdMap())
	if err != nil {
		impl.logger.Errorw("error in getting kind and subKind by devtronResourceSchemaId", "err", err, "devtronResourceSchemaId", existingResourceObject.DevtronResourceSchemaId)
		return err
	}
	identifier, err := impl.getResourceObjectIdentifier(kind, subKind, version, existingResourceObject)
	if err != nil {
		impl.logger.Errorw("error in building identifier for ResourceObject", "err", err,
			"kind", kind, "subKind", subKind, "version", version, "existingResourceObjectId", existingResourceObject.Id)
		return err
	}
	err = impl.dtResObjectRepository.UpdateIdentifier(existingResourceObject.Id, existingResourceObject.DevtronResourceSchemaId, identifier)
	if err != nil {
		impl.logger.Errorw("error in updating identifier for ResourceObject", "err", err,
			"existingResourceObjectId", existingResourceObject.Id)
		return err
	}
	existingResourceObject.Identifier = identifier
	return nil
}

func (impl *DevtronResourceServiceImpl) findAppIdFromDependencyInfo(dependencyInfo *bean.DepInfo) (int, error) {
	id := dependencyInfo.Id
	var err error
	if id == 0 {
		// identifier is app_name for application dependency
		id, err = impl.appRepository.FindIdByNameAndAppType(dependencyInfo.Identifier, helper2.CustomApp)
	}
	return id, err
}

func (impl *DevtronResourceServiceImpl) getDepOptions(req *bean.DtResObjDescApiBean, query *apiBean.GetDependencyOptionsQueryParams) ([]*bean.DepOptions, error) {
	f := getFuncToGetDepOptionsData(req.Kind, "", req.Version)
	if f != nil {
		depOptions, err := f(impl, req, query)
		if err != nil {
			return nil, err
		}
		return depOptions, nil
	} else {
		return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.UnimplementedResourceKindOrVersion, bean.UnimplementedResourceKindOrVersion)
	}
}

func (impl *DevtronResourceServiceExtendedImpl) getConfigInternalDataByParentResourceType(parentResourceType *bean.DtResTypeReq, configDataJsonObj string, isLite bool) (configData *bean.DepConfigInternalBean, err error) {
	f := getFuncToUpdateDepConfigInternalData(parentResourceType.ResourceKind.ToString(),
		parentResourceType.ResourceSubKind.ToString(), parentResourceType.ResourceVersion.ToString())
	if f != nil {
		configData = &bean.DepConfigInternalBean{}
		err = f(impl, configDataJsonObj, configData, isLite)
		if err != nil {
			return nil, err
		}
	}
	return configData, nil
}

func getChildInheritanceData(dependency string) ([]*bean.ChildInheritance, error) {
	childInheritance := make([]*bean.ChildInheritance, 0)
	inheritance := gjson.Get(dependency, bean.DependencyChildInheritanceKey).String()
	if len(inheritance) != 0 {
		err := json.Unmarshal([]byte(inheritance), &childInheritance)
		if err != nil {
			return childInheritance, err
		}
	}
	return childInheritance, nil
}

// common helper methods ends
//

// task run methods end.
//

// ExecuteTask empty implementation
func (impl *DevtronResourceServiceImpl) ExecuteTask(ctx context.Context, req *bean.DevResTaskExecutionBean, authorisedTaskTargetIds map[string]bool, processednfo *TaskExecutionProcessInfo, existingObject *repository.DevtronResourceObject, userMetadata *bean2.UserMetadata) ([]*bean.TaskExecutionResponseBean, error) {
	return nil, fmt.Errorf("implementation not supported")
}

// GetTaskRunInfoWithFilters empty implementation
func (impl *DevtronResourceServiceImpl) GetTaskRunInfoWithFilters(req *bean.TaskInfoPostApiBean, query *apiBean.GetTaskRunInfoQueryParams, userId int32) (*bean.DeploymentTaskInfoResponse, error) {
	return nil, fmt.Errorf("implementation not supported")
}

// GetProcessInfoForRbacAndTaskExecution empty implementation
func (impl *DevtronResourceServiceImpl) GetProcessInfoForRbacAndTaskExecution(req *bean.DevResTaskExecutionBean, userId int32) (*TaskExecutionProcessInfo, *repository.DevtronResourceObject, error) {
	return nil, nil, fmt.Errorf("implementation not supported")
}

// moved here due to cyclic dependency
type TaskExecutionProcessInfo struct {
	PipelineIdVsPipelineMap              map[int]*pipelineConfig.Pipeline
	MapOfTenantInstallationIdKeyVsEnvs   map[string]*bean.EnvIdsAndDeploymentConfig
	AppEnvIdVsPipelineInfo               map[string]*bean.CdPipelineReleaseInfo
	AppVsArtifactIdMap                   map[int]int
	ArtifactIdVsArtifactMap              map[int]*repository3.CiArtifact
	AppIdVsDrSchemaDetail                map[int]*bean.DepDetail
	AppIdVsDepDetail                     map[int]*bean.DtResDepJsonBean
	AllAppIdsFromRelease                 []int
	InternalDescBean                     *bean.DtResObjectInternalDescBean
	TotalPipelineIds                     []int
	MapOfTenantInstallationVsPipelineIds map[string][]int
}

// task run methods end.
